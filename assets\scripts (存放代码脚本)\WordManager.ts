import { _decorator, Component, Node, Label, Color } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 单词管理器 - 最初简单版本
 * 基本功能：显示单词列表
 */
@ccclass('WordManager')
export class WordManager extends Component {
    @property({type: [Node], tooltip: '单词显示节点列表'})
    wordNodes: Node[] = [];
    
    @property({type: [String], tooltip: '单词列表'})
    words: string[] = ['CAT', 'DOG', 'BIRD', 'FISH', 'LION', 'BEAR'];

    start() {
        console.log("WordManager 启动");
        
        // 显示单词
        this.displayWords();
        
        console.log("WordManager 初始化完成");
    }

    // 显示单词到界面上
    displayWords() {
        console.log("显示单词到界面");
        
        for (let i = 0; i < Math.min(this.words.length, this.wordNodes.length); i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.string = this.words[i];
                    label.color = new Color(0, 0, 0, 255); // 黑色
                    wordNode.active = true;
                    console.log(`设置单词 ${i}: ${this.words[i]}`);
                }
            }
        }
        
        // 隐藏多余的节点
        for (let i = this.words.length; i < this.wordNodes.length; i++) {
            if (this.wordNodes[i]) {
                this.wordNodes[i].active = false;
            }
        }
    }
}
