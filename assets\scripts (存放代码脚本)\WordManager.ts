import { _decorator, Component, Node, Label, Color, Layout, UITransform, director, Sprite } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('WordManager')
export class WordManager extends Component {
    @property({type: [Node], tooltip: '单词显示节点列表'})
    wordNodes: Node[] = [];
    
    @property({tooltip: '单词列表'})
    words: string[] = [];
    
    @property({tooltip: '是否使用预设单词'})
    usePresetWords: boolean = true;
    
    @property({tooltip: '预设单词列表', visible: function(this: any) { return this.usePresetWords; }})
    presetWords: string[] = ['CAT', 'BANANA', 'ELEPHANT', 'DOG', 'STRAWBERRY', 'OWL'];
    
    @property({type: Color, tooltip: '已找到单词的颜色'})
    foundWordColor: Color = new Color(0, 255, 0, 255);

    // 存储已找到单词的字母位置，用于保持高亮
    private foundWordPositions: Map<string, {row: number, col: number}[]> = new Map();

    // 记录已找到的单词
    private foundWords: Set<string> = new Set();
    
    onLoad() {
        // 确保父节点有 Layout 组件
        const layout = this.node.getComponent(Layout);
        if (!layout) {
            const newLayout = this.node.addComponent(Layout);
            newLayout.type = Layout.Type.HORIZONTAL;
            newLayout.resizeMode = Layout.ResizeMode.CONTAINER;
            newLayout.horizontalDirection = Layout.HorizontalDirection.LEFT_TO_RIGHT;
            newLayout.spacingX = 20; // 单词之间的间距
        }
    }
    
    start() {
        console.log("WordManager 开始初始化");
        
        // 确保单元格有合适的布局
        this.setupCellLayout();
        
        // 如果使用预设单词，直接使用预设单词列表
        if (this.usePresetWords) {
            this.words = [...this.presetWords];
        } else {
            // 否则生成随机英语单词
            this.words = this.getRandomWords(6);
        }
        
        console.log("使用的单词:", this.words);
        
        // 显示单词
        this.displayWords();

        // 不在初始化时通知，避免重复生成
        // 字母网格已经在 start() 时预生成了
        console.log("WordManager 初始化完成，跳过初始通知");
        
        // 监听单词选择事件
        this.node.on('word-selected', this.onWordSelected, this);
        
        console.log("WordManager 初始化完成");
    }
    
    // 显示单词到界面上
    displayWords() {
        console.log("尝试显示单词到界面");
        
        if (this.wordNodes.length === 0) {
            console.error("未设置单词节点列表");
            return;
        }
        
        console.log("使用多个单词显示节点");
        for (let i = 0; i < Math.min(this.words.length, this.wordNodes.length); i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    console.log(`设置单词节点 ${i} 的文本:`, this.words[i]);
                    label.string = this.words[i];
                    
                    // 确保单词节点是可见的
                    wordNode.active = true;
                } else {
                    console.error(`单词节点 ${i} 没有Label组件`);
                }
            }
        }
        
        // 如果单词数量少于节点数量，隐藏多余的节点
        for (let i = this.words.length; i < this.wordNodes.length; i++) {
            if (this.wordNodes[i]) {
                this.wordNodes[i].active = false;
            }
        }
    }
    
    // 处理玩家选择的单词
    onWordSelected(wordData: {word: string, cells: {row: number, col: number}[]}) {
        if (!wordData || !wordData.word) {
            console.error('无效的单词数据');
            return;
        }

        const word = wordData.word.toUpperCase();
        console.log(`玩家选择了单词: ${word}`);

        // 检查是否是有效单词
        const isValid = this.checkWord(word);

        if (isValid) {
            console.log(`找到有效单词: ${word}`);
            // 标记单词为已找到
            this.markWordAsFound(word, wordData.cells);
            // 检查是否所有单词都已找到
            this.checkAllWordsFound();
        } else {
            console.log(`无效单词: ${word}`);
        }

        // 发送验证结果
        this.node.emit('word-validation-result', isValid);
    }
    
    // 标记单词为已找到
    markWordAsFound(word: string, cells?: {row: number, col: number}[]) {
        console.log(`标记单词 ${word} 为已找到`);

        // 添加到已找到集合
        this.foundWords.add(word);

        // 如果提供了单元格位置，保存它们用于高亮
        if (cells) {
            this.foundWordPositions.set(word, [...cells]);
            this.highlightWordCells(word, cells);
        }

        // 更新单词显示颜色
        this.updateWordDisplay(word);
    }

    // 高亮单词的字母单元格
    private highlightWordCells(word: string, cells: {row: number, col: number}[]) {
        console.log(`高亮单词 ${word} 的字母单元格`);

        // 获取字母网格管理器
        const letterGridNode = this.getLetterGridNode();
        if (!letterGridNode) {
            console.warn('未找到字母网格节点');
            return;
        }

        const letterGridManager = letterGridNode.getComponent('LetterGridManager') as any;
        if (!letterGridManager || !letterGridManager.cellNodes) {
            console.warn('未找到字母网格管理器或单元格节点');
            return;
        }

        // 通知字母网格管理器记录这些位置
        if (letterGridManager.addFoundWordCells) {
            letterGridManager.addFoundWordCells(cells);
        }

        // 高亮每个字母单元格
        cells.forEach((cell, index) => {
            const cellNode = letterGridManager.cellNodes[cell.row]?.[cell.col];
            if (cellNode) {
                const sprite = cellNode.getComponent(Sprite);
                if (sprite) {
                    // 设置高亮颜色
                    sprite.color = this.foundWordColor;
                    console.log(`高亮单元格 [${cell.row},${cell.col}] 为绿色`);
                }
            }
        });
    }

    // 更新单词显示
    private updateWordDisplay(word: string) {
        const index = this.words.indexOf(word);

        if (index >= 0 && index < this.wordNodes.length) {
            const wordNode = this.wordNodes[index];

            if (wordNode) {
                const label = wordNode.getComponent(Label);

                if (label) {
                    label.color = this.foundWordColor;
                    console.log(`✅ 单词 ${word} 显示颜色已更新为绿色`);
                } else {
                    console.error(`❌ 单词节点 ${index} 没有 Label 组件`);
                }
            } else {
                console.error(`❌ 单词节点 ${index} 为空`);
            }
        } else {
            console.error(`❌ 单词 ${word} 不在列表中或索引超出范围`);
        }
    }

    // 获取字母网格节点
    private getLetterGridNode(): Node | null {
        // 尝试多个可能的节点名称
        const possibleNames = ['字母区域', '下排字母', 'LetterGrid', '字母网格', '字母表'];

        // 先在同级节点中查找
        if (this.node.parent) {
            for (const name of possibleNames) {
                const node = this.node.parent.getChildByName(name);
                if (node) {
                    return node;
                }
            }
        }

        // 在 Canvas 下查找
        const scene = director.getScene();
        if (scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                for (const name of possibleNames) {
                    const node = canvas.getChildByName(name);
                    if (node) {
                        return node;
                    }
                }
            }
        }

        return null;
    }
    
    // 检查是否所有单词都已找到
    checkAllWordsFound() {
        if (this.foundWords.size === this.words.length) {
            console.log("所有单词都已找到！");
            // 发送关卡完成事件
            this.node.emit('level-completed');
        }
    }
    
    // 检查单词是否在单词列表中
    checkWord(word: string): boolean {
        // 转换为大写进行比较
        const upperWord = word.toUpperCase();
        // 检查是否在单词列表中且尚未找到
        return this.words.indexOf(upperWord) !== -1 && !this.foundWords.has(upperWord);
    }
    
    // 获取随机单词（示例实现）
    getRandomWords(count: number): string[] {
        // 这里应该实现一个真正的随机单词生成器
        // 这只是一个示例
        const sampleWords = [
            'APPLE', 'BANANA', 'CHERRY', 'ORANGE', 'GRAPE', 'LEMON',
            'MELON', 'PEACH', 'PLUM', 'KIWI', 'MANGO', 'PEAR'
        ];
        
        // 随机选择单词
        const result: string[] = [];
        const available = [...sampleWords];
        
        for (let i = 0; i < count && available.length > 0; i++) {
            const index = Math.floor(Math.random() * available.length);
            result.push(available[index]);
            available.splice(index, 1);
        }
        
        return result;
    }
    
    // 重置游戏
    resetGame() {
        // 清空已找到的单词
        this.foundWords.clear();

        // 清空已找到单词的位置记录
        this.foundWordPositions.clear();

        // 重新生成单词
        if (!this.usePresetWords) {
            this.words = this.getRandomWords(6);
        }

        // 重新显示单词
        this.displayWords();

        // 通知字母网格重新生成（只在重置时通知）
        this.scheduleOnce(() => {
            console.log("重置游戏，通知字母网格重新生成");
            this.notifyWordsReady();
        }, 0.1);
    }

    // 公共方法：重新生成游戏
    public regenerateGame() {
        console.log("重新生成游戏");

        // 如果使用预设单词，可以打乱顺序或选择不同的单词
        if (this.usePresetWords) {
            // 打乱预设单词顺序
            this.shuffleArray(this.presetWords);
            this.words = [...this.presetWords];
        } else {
            // 生成新的随机单词
            this.words = this.getRandomWords(6);
        }

        // 重置游戏状态
        this.resetGame();
    }

    // 工具方法：打乱数组
    private shuffleArray<T>(array: T[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    onDestroy() {
        // 移除事件监听
        if (this.node) {
            this.node.off('word-selected', this.onWordSelected, this);
            this.node.off('word-validation-result', null);
            this.node.off('level-completed', null);
        }
    }

    // 添加新方法设置单元格布局
    private setupCellLayout() {
        // 遍历所有单词节点
        for (let i = 0; i < this.wordNodes.length; i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode && wordNode.isValid) {
                // 获取单元格节点（父节点）
                const cellNode = wordNode.parent;
                if (cellNode && cellNode.isValid) {
                    // 确保单元格有 UITransform 组件
                    let cellTransform = cellNode.getComponent(UITransform);
                    if (!cellTransform) {
                        cellTransform = cellNode.addComponent(UITransform);
                    }
                    
                    // 设置单元格的最小宽度，确保短单词也有足够的显示空间
                    cellTransform.width = Math.max(cellTransform.width, 120);
                    
                    // 确保单词节点填满单元格
                    let wordTransform = wordNode.getComponent(UITransform);
                    if (!wordTransform) {
                        wordTransform = wordNode.addComponent(UITransform);
                    }
                    wordTransform.width = cellTransform.width;
                    wordTransform.height = cellTransform.height;
                    
                    // 确保单词有 Label 组件并设置居中
                    const label = wordNode.getComponent(Label);
                    if (label) {
                        label.horizontalAlign = Label.HorizontalAlign.CENTER;
                        label.verticalAlign = Label.VerticalAlign.CENTER;
                        
                        // 设置自动换行和溢出处理
                        label.overflow = Label.Overflow.SHRINK;
                        label.enableWrapText = false;
                    }
                }
            }
        }
    }

    // 添加通知字母网格的方法
    private notifyWordsReady() {
        console.log("准备通知字母网格单词列表:", this.words);
        
        // 1. 发送全局事件通知字母网格
        const scene = director.getScene();
        if (scene) {
            console.log("发送单词列表准备好事件:", this.words);
            scene.emit('words-ready', this.words);
        }
        
        // 2. 尝试直接查找字母网格节点
        const possibleNames = ['字母区域', '下排字母', 'LetterGrid', '字母网格', '字母表'];
        let letterGridNode = null;
        
        // 在同一父节点下查找
        for (const name of possibleNames) {
            const node = this.node.parent.getChildByName(name);
            if (node) {
                console.log(`找到字母网格节点: ${name}`);
                letterGridNode = node;
                break;
            }
        }
        
        // 如果没找到，尝试在 Canvas 下查找
        if (!letterGridNode && scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                for (const name of possibleNames) {
                    const node = canvas.getChildByName(name);
                    if (node) {
                        console.log(`在 Canvas 下找到字母网格节点: ${name}`);
                        letterGridNode = node;
                        break;
                    }
                }
            }
        }
        
        // 如果找到了节点，尝试直接调用方法
        if (letterGridNode) {
            const letterGrid = letterGridNode.getComponent('LetterGridManager');
            if (letterGrid) {
                console.log("直接通知字母网格单词列表:", this.words);
                letterGrid.onWordsReady(this.words);
            } else {
                console.warn("找到字母网格节点，但没有 LetterGridManager 组件");
            }
        } else {
            console.warn("未找到字母网格节点");
        }
        
        // 3. 添加调试日志
        console.log("场景结构:");
        this.logSceneStructure(scene, 0);
    }

    // 添加调试方法，打印场景结构
    private logSceneStructure(node: Node, depth: number) {
        if (!node) return;
        
        const indent = '  '.repeat(depth);
        console.log(`${indent}${node.name}`);
        
        for (let i = 0; i < node.children.length; i++) {
            this.logSceneStructure(node.children[i], depth + 1);
        }
    }

    // 添加一个公共方法，可以从控制台直接调用
    public debugSendWords() {
        console.log("手动触发单词列表通知");
        this.notifyWordsReady();
    }
}













