import { _decorator, Component, Node, Graphics, Color, Vec2, Vec3, UITransform, tween, Sprite, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 视觉效果管理器
 * 负责处理连线动画、颜色变化、缩放效果等视觉反馈
 */
@ccclass('VisualEffectsManager')
export class VisualEffectsManager extends Component {
    @property({type: Node, tooltip: '连线图层'})
    lineLayer: Node = null;
    
    @property({tooltip: '连线宽度'})
    lineWidth: number = 8;
    
    @property({type: Color, tooltip: '默认连线颜色'})
    defaultLineColor: Color = new Color(255, 165, 0, 255); // 橙色
    
    @property({type: Color, tooltip: '成功连线颜色'})
    successLineColor: Color = new Color(0, 255, 0, 255); // 绿色
    
    @property({type: Color, tooltip: '失败连线颜色'})
    failLineColor: Color = new Color(255, 0, 0, 255); // 红色
    
    @property({type: Color, tooltip: '选中单元格颜色'})
    selectedCellColor: Color = new Color(255, 200, 0, 180); // 半透明黄色
    
    @property({type: Color, tooltip: '默认单元格颜色'})
    defaultCellColor: Color = new Color(255, 255, 255, 255); // 白色
    
    @property({tooltip: '动画持续时间'})
    animationDuration: number = 0.3;
    
    @property({tooltip: '连线动画速度'})
    lineAnimationSpeed: number = 500; // 像素/秒
    
    // Graphics组件用于绘制连线
    private graphics: Graphics = null;
    // 当前连线路径
    private currentPath: Vec2[] = [];
    // 动画相关
    private isAnimating: boolean = false;
    private animationProgress: number = 0;
    
    start() {
        this.initLineGraphics();
    }
    
    /**
     * 初始化连线图层
     */
    private initLineGraphics() {
        if (!this.lineLayer) {
            // 尝试查找现有的连线图层
            this.lineLayer = this.node.getChildByName('连线图层');
            if (!this.lineLayer) {
                // 如果没找到，自动创建
                this.lineLayer = new Node('连线图层');
                this.lineLayer.parent = this.node;
                console.log('VisualEffectsManager: 已自动创建连线图层');
            } else {
                console.log('VisualEffectsManager: 找到现有连线图层');
            }
        } else {
            console.log('VisualEffectsManager: 使用预设连线图层');
        }
        
        // 添加Graphics组件
        this.graphics = this.lineLayer.getComponent(Graphics);
        if (!this.graphics) {
            this.graphics = this.lineLayer.addComponent(Graphics);
        }
        
        // 设置线条样式
        this.setupLineStyle(this.defaultLineColor);
        
        console.log('视觉效果管理器初始化完成');
    }
    
    /**
     * 设置线条样式
     */
    private setupLineStyle(color: Color) {
        if (!this.graphics) return;
        
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.lineJoin = Graphics.LineJoin.ROUND;
        this.graphics.lineCap = Graphics.LineCap.ROUND;
        this.graphics.strokeColor = color;
    }
    
    /**
     * 开始绘制连线
     */
    public startDrawingLine(startPos: Vec2) {
        if (!this.graphics) return;
        
        this.graphics.clear();
        this.currentPath = [startPos];
        this.setupLineStyle(this.defaultLineColor);
        
        // 绘制起始点
        this.graphics.moveTo(startPos.x, startPos.y);
    }
    
    /**
     * 更新连线路径
     */
    public updateLine(positions: Vec2[], currentTouchPos?: Vec2) {
        if (!this.graphics || positions.length === 0) return;
        
        this.graphics.clear();
        this.currentPath = [...positions];
        this.setupLineStyle(this.defaultLineColor);
        
        // 转换世界坐标到连线图层本地坐标
        const localPositions = positions.map(pos => 
            this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(pos.x, pos.y, 0))
        );
        
        if (localPositions.length > 0) {
            this.graphics.moveTo(localPositions[0].x, localPositions[0].y);
            
            // 绘制连线路径
            for (let i = 1; i < localPositions.length; i++) {
                this.graphics.lineTo(localPositions[i].x, localPositions[i].y);
            }
            
            // 如果有当前触摸位置，连接到触摸位置
            if (currentTouchPos) {
                const touchLocalPos = this.lineLayer.getComponent(UITransform)
                    .convertToNodeSpaceAR(new Vec3(currentTouchPos.x, currentTouchPos.y, 0));
                this.graphics.lineTo(touchLocalPos.x, touchLocalPos.y);
            }
            
            this.graphics.stroke();
        }
    }
    
    /**
     * 显示连线结果（成功或失败）
     */
    public showLineResult(isSuccess: boolean, positions: Vec2[], callback?: () => void) {
        if (!this.graphics || positions.length === 0) return;
        
        // 设置结果颜色
        const resultColor = isSuccess ? this.successLineColor : this.failLineColor;
        this.setupLineStyle(resultColor);
        
        // 重新绘制连线
        this.graphics.clear();
        const localPositions = positions.map(pos => 
            this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(pos.x, pos.y, 0))
        );
        
        if (localPositions.length > 0) {
            this.graphics.moveTo(localPositions[0].x, localPositions[0].y);
            for (let i = 1; i < localPositions.length; i++) {
                this.graphics.lineTo(localPositions[i].x, localPositions[i].y);
            }
            this.graphics.stroke();
        }
        
        // 如果是成功的连线，播放成功动画
        if (isSuccess) {
            this.playSuccessLineAnimation(positions, callback);
        } else {
            // 失败的连线，短暂显示后清除
            this.scheduleOnce(() => {
                this.clearLine();
                if (callback) callback();
            }, 0.5);
        }
    }
    
    /**
     * 播放成功连线动画
     */
    private playSuccessLineAnimation(positions: Vec2[], callback?: () => void) {
        // 连线闪烁效果
        let blinkCount = 0;
        const maxBlinks = 3;
        
        const blinkInterval = setInterval(() => {
            if (blinkCount >= maxBlinks) {
                clearInterval(blinkInterval);
                // 保持连线显示一段时间后清除
                this.scheduleOnce(() => {
                    this.clearLine();
                    if (callback) callback();
                }, 0.8);
                return;
            }
            
            // 切换可见性
            this.lineLayer.active = !this.lineLayer.active;
            blinkCount++;
        }, 0.15);
    }
    
    /**
     * 清除连线
     */
    public clearLine() {
        if (this.graphics) {
            this.graphics.clear();
        }
        this.currentPath = [];
        this.lineLayer.active = true; // 确保图层可见
    }
    
    /**
     * 高亮单元格
     */
    public highlightCell(cellNode: Node, highlight: boolean = true) {
        if (!cellNode) return;
        
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = highlight ? this.selectedCellColor : this.defaultCellColor;
        }
    }
    
    /**
     * 播放单元格选中动画
     */
    public playCellSelectAnimation(cellNode: Node) {
        if (!cellNode) return;
        
        // 缩放动画
        const originalScale = cellNode.scale.clone();
        tween(cellNode)
            .to(0.1, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.1, { scale: originalScale })
            .start();
    }
    
    /**
     * 播放单词找到动画
     */
    public playWordFoundAnimation(cellNodes: Node[], callback?: () => void) {
        if (!cellNodes || cellNodes.length === 0) return;
        
        let completedAnimations = 0;
        const totalAnimations = cellNodes.length;
        
        cellNodes.forEach((cellNode, index) => {
            if (!cellNode) return;
            
            // 延迟播放，创建波浪效果
            this.scheduleOnce(() => {
                const originalScale = cellNode.scale.clone();
                
                // 播放弹跳动画
                tween(cellNode)
                    .to(0.2, { scale: new Vec3(1.3, 1.3, 1) })
                    .to(0.2, { scale: new Vec3(0.9, 0.9, 1) })
                    .to(0.1, { scale: originalScale })
                    .call(() => {
                        completedAnimations++;
                        if (completedAnimations === totalAnimations && callback) {
                            callback();
                        }
                    })
                    .start();
                    
                // 同时播放颜色动画
                const sprite = cellNode.getComponent(Sprite);
                if (sprite) {
                    const originalColor = sprite.color.clone();
                    tween(sprite)
                        .to(0.2, { color: this.successLineColor })
                        .to(0.3, { color: originalColor })
                        .start();
                }
            }, index * 0.1);
        });
    }
    
    /**
     * 播放单词验证失败动画
     */
    public playWordFailAnimation(cellNodes: Node[], callback?: () => void) {
        if (!cellNodes || cellNodes.length === 0) return;
        
        cellNodes.forEach((cellNode, index) => {
            if (!cellNode) return;
            
            // 震动动画
            const originalPos = cellNode.position.clone();
            tween(cellNode)
                .to(0.05, { position: new Vec3(originalPos.x + 5, originalPos.y, originalPos.z) })
                .to(0.05, { position: new Vec3(originalPos.x - 5, originalPos.y, originalPos.z) })
                .to(0.05, { position: new Vec3(originalPos.x + 3, originalPos.y, originalPos.z) })
                .to(0.05, { position: new Vec3(originalPos.x - 3, originalPos.y, originalPos.z) })
                .to(0.05, { position: originalPos })
                .call(() => {
                    if (index === cellNodes.length - 1 && callback) {
                        callback();
                    }
                })
                .start();
        });
    }
    
    /**
     * 重置所有单元格的视觉状态
     */
    public resetAllCells(cellNodes: Node[][]) {
        if (!cellNodes) return;
        
        for (let row = 0; row < cellNodes.length; row++) {
            for (let col = 0; col < cellNodes[row].length; col++) {
                const cellNode = cellNodes[row][col];
                if (cellNode) {
                    this.highlightCell(cellNode, false);
                }
            }
        }
    }
    
    onDestroy() {
        // 清理资源
        this.clearLine();
    }
}
