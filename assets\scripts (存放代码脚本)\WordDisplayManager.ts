import { _decorator, Component, Node, Label, Color, tween, Vec3, Sprite, UITransform } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 单词显示管理器
 * 负责处理已找到单词的多彩显示和视觉反馈
 */
@ccclass('WordDisplayManager')
export class WordDisplayManager extends Component {
    @property({type: [Node], tooltip: '单词显示节点列表'})
    wordNodes: Node[] = [];
    
    @property({type: [Color], tooltip: '多彩颜色列表'})
    wordColors: Color[] = [
        new Color(255, 87, 87, 255),   // 红色
        new Color(255, 193, 7, 255),   // 黄色
        new Color(76, 175, 80, 255),   // 绿色
        new Color(33, 150, 243, 255),  // 蓝色
        new Color(156, 39, 176, 255),  // 紫色
        new Color(255, 152, 0, 255),   // 橙色
        new Color(0, 188, 212, 255),   // 青色
        new Color(233, 30, 99, 255),   // 粉色
    ];
    
    @property({type: Color, tooltip: '默认单词颜色'})
    defaultWordColor: Color = new Color(0, 0, 0, 255); // 黑色
    
    @property({type: Color, tooltip: '单词背景高亮颜色'})
    highlightBackgroundColor: Color = new Color(255, 255, 255, 100); // 半透明白色
    
    @property({tooltip: '找到单词时的动画持续时间'})
    foundAnimationDuration: number = 0.5;
    
    @property({tooltip: '单词显示动画延迟'})
    displayAnimationDelay: number = 0.1;
    
    // 记录已找到的单词及其颜色
    private foundWordsMap: Map<string, Color> = new Map();
    private colorIndex: number = 0;
    
    start() {
        console.log('单词显示管理器初始化');
        this.initializeWordDisplay();
    }
    
    /**
     * 初始化单词显示
     */
    private initializeWordDisplay() {
        // 确保所有单词节点都有正确的初始状态
        this.wordNodes.forEach((wordNode, index) => {
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.color = this.defaultWordColor;
                }

                // 为单词节点创建背景子节点用于高亮效果
                this.ensureBackgroundNode(wordNode);
            }
        });
    }

    /**
     * 确保单词节点有背景子节点
     */
    private ensureBackgroundNode(wordNode: Node) {
        let backgroundNode = wordNode.getChildByName('WordBackground');
        if (!backgroundNode) {
            backgroundNode = new Node('WordBackground');
            backgroundNode.parent = wordNode;
            backgroundNode.setSiblingIndex(0); // 放在最底层

            // 添加 UITransform 组件
            const backgroundTransform = backgroundNode.addComponent(UITransform);
            const wordTransform = wordNode.getComponent(UITransform);
            if (wordTransform) {
                backgroundTransform.width = wordTransform.width;
                backgroundTransform.height = wordTransform.height;
            }

            // 添加 Sprite 组件
            const sprite = backgroundNode.addComponent(Sprite);
            sprite.color = new Color(255, 255, 255, 0); // 透明背景

            backgroundNode.setPosition(0, 0, 0);
        }
    }
    
    /**
     * 设置单词列表
     */
    public setWords(words: string[]) {
        console.log('设置单词列表:', words);
        
        // 重置状态
        this.foundWordsMap.clear();
        this.colorIndex = 0;
        
        // 显示单词到界面
        for (let i = 0; i < Math.min(words.length, this.wordNodes.length); i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.string = words[i];
                    label.color = this.defaultWordColor;
                    wordNode.active = true;
                    
                    // 播放单词出现动画
                    this.playWordAppearAnimation(wordNode, i);
                } else {
                    console.error(`单词节点 ${i} 没有Label组件`);
                }
            }
        }
        
        // 隐藏多余的节点
        for (let i = words.length; i < this.wordNodes.length; i++) {
            if (this.wordNodes[i]) {
                this.wordNodes[i].active = false;
            }
        }
    }
    
    /**
     * 播放单词出现动画
     */
    private playWordAppearAnimation(wordNode: Node, index: number) {
        // 初始状态：缩放为0
        wordNode.scale = new Vec3(0, 0, 1);
        
        // 延迟播放动画，创建依次出现的效果
        this.scheduleOnce(() => {
            tween(wordNode)
                .to(0.3, { scale: new Vec3(1.1, 1.1, 1) }, { easing: 'backOut' })
                .to(0.1, { scale: new Vec3(1, 1, 1) })
                .start();
        }, index * this.displayAnimationDelay);
    }
    
    /**
     * 标记单词为已找到
     */
    public markWordAsFound(word: string, wordIndex?: number): boolean {
        const upperWord = word.toUpperCase();
        
        // 如果已经找到过，不重复处理
        if (this.foundWordsMap.has(upperWord)) {
            return false;
        }
        
        // 分配颜色
        const color = this.getNextColor();
        this.foundWordsMap.set(upperWord, color);
        
        // 查找单词节点
        let targetIndex = wordIndex;
        if (targetIndex === undefined) {
            targetIndex = this.findWordNodeIndex(upperWord);
        }
        
        if (targetIndex >= 0 && targetIndex < this.wordNodes.length) {
            const wordNode = this.wordNodes[targetIndex];
            this.playWordFoundAnimation(wordNode, color);
            return true;
        } else {
            console.warn(`未找到单词 ${upperWord} 对应的显示节点`);
            return false;
        }
    }
    
    /**
     * 查找单词节点索引
     */
    private findWordNodeIndex(word: string): number {
        for (let i = 0; i < this.wordNodes.length; i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode && wordNode.active) {
                const label = wordNode.getComponent(Label);
                if (label && label.string.toUpperCase() === word) {
                    return i;
                }
            }
        }
        return -1;
    }
    
    /**
     * 获取下一个颜色
     */
    private getNextColor(): Color {
        const color = this.wordColors[this.colorIndex % this.wordColors.length];
        this.colorIndex++;
        return color;
    }
    
    /**
     * 播放单词找到动画
     */
    private playWordFoundAnimation(wordNode: Node, color: Color) {
        if (!wordNode) return;
        
        const label = wordNode.getComponent(Label);
        const backgroundNode = wordNode.getChildByName('WordBackground');
        const sprite = backgroundNode?.getComponent(Sprite);
        
        if (label) {
            // 保存原始颜色
            const originalColor = label.color.clone();
            
            // 播放颜色变化动画
            tween(label)
                .to(0.2, { color: new Color(255, 255, 255, 255) }) // 先变白
                .to(0.3, { color: color }) // 再变成目标颜色
                .start();
        }
        
        if (sprite) {
            // 播放背景高亮动画
            tween(sprite)
                .to(0.1, { color: this.highlightBackgroundColor })
                .to(0.4, { color: new Color(255, 255, 255, 0) }) // 渐变回透明
                .start();
        }
        
        // 播放缩放动画
        const originalScale = wordNode.scale.clone();
        tween(wordNode)
            .to(0.2, { scale: new Vec3(1.3, 1.3, 1) })
            .to(0.2, { scale: new Vec3(0.9, 0.9, 1) })
            .to(0.1, { scale: originalScale })
            .start();
        
        // 播放旋转动画（轻微摆动）
        tween(wordNode)
            .to(0.1, { eulerAngles: new Vec3(0, 0, 5) })
            .to(0.2, { eulerAngles: new Vec3(0, 0, -5) })
            .to(0.1, { eulerAngles: new Vec3(0, 0, 0) })
            .start();
        
        console.log(`单词 ${label?.string} 已标记为找到，使用颜色:`, color);
    }
    
    /**
     * 播放所有单词完成动画
     */
    public playAllWordsFoundAnimation(callback?: () => void) {
        console.log('播放所有单词完成动画');
        
        let completedAnimations = 0;
        const totalWords = this.wordNodes.filter(node => node && node.active).length;
        
        this.wordNodes.forEach((wordNode, index) => {
            if (wordNode && wordNode.active) {
                this.scheduleOnce(() => {
                    // 播放庆祝动画
                    const originalScale = wordNode.scale.clone();
                    tween(wordNode)
                        .to(0.3, { scale: new Vec3(1.2, 1.2, 1) })
                        .to(0.2, { scale: new Vec3(1.1, 1.1, 1) })
                        .to(0.3, { scale: originalScale })
                        .call(() => {
                            completedAnimations++;
                            if (completedAnimations === totalWords && callback) {
                                callback();
                            }
                        })
                        .start();
                    
                    // 播放彩虹色彩动画
                    const label = wordNode.getComponent(Label);
                    if (label) {
                        const colors = [
                            new Color(255, 0, 0, 255),   // 红
                            new Color(255, 165, 0, 255), // 橙
                            new Color(255, 255, 0, 255), // 黄
                            new Color(0, 255, 0, 255),   // 绿
                            new Color(0, 0, 255, 255),   // 蓝
                            new Color(75, 0, 130, 255),  // 靛
                            new Color(238, 130, 238, 255) // 紫
                        ];
                        
                        let colorIndex = 0;
                        const colorInterval = setInterval(() => {
                            if (colorIndex >= colors.length) {
                                clearInterval(colorInterval);
                                // 恢复原来的找到颜色
                                const foundColor = this.foundWordsMap.get(label.string.toUpperCase());
                                if (foundColor) {
                                    label.color = foundColor;
                                }
                                return;
                            }
                            label.color = colors[colorIndex];
                            colorIndex++;
                        }, 100);
                    }
                }, index * 0.1);
            }
        });
    }
    
    /**
     * 重置所有单词显示
     */
    public resetAllWords() {
        console.log('重置所有单词显示');
        
        this.foundWordsMap.clear();
        this.colorIndex = 0;
        
        this.wordNodes.forEach(wordNode => {
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                const backgroundNode = wordNode.getChildByName('WordBackground');
                const sprite = backgroundNode?.getComponent(Sprite);

                if (label) {
                    label.color = this.defaultWordColor;
                }

                if (sprite) {
                    sprite.color = new Color(255, 255, 255, 0);
                }

                // 重置变换
                wordNode.scale = new Vec3(1, 1, 1);
                wordNode.eulerAngles = new Vec3(0, 0, 0);
            }
        });
    }
    
    /**
     * 获取已找到的单词数量
     */
    public getFoundWordsCount(): number {
        return this.foundWordsMap.size;
    }
    
    /**
     * 检查是否所有单词都已找到
     */
    public areAllWordsFound(): boolean {
        const activeWordCount = this.wordNodes.filter(node => node && node.active).length;
        return this.foundWordsMap.size === activeWordCount;
    }
    
    /**
     * 获取已找到单词的颜色
     */
    public getWordColor(word: string): Color | null {
        return this.foundWordsMap.get(word.toUpperCase()) || null;
    }
    
    onDestroy() {
        // 清理资源
        this.foundWordsMap.clear();
    }
}
