import { _decorator, Component, Node, Label, Sprite, Color, UITransform, Graphics, Vec2, Vec3, EventTouch, tween, director } from 'cc';
import { SimpleHighlightManager } from './SimpleHighlightManager';
const { ccclass, property } = _decorator;

@ccclass('LetterGridManager')
export class LetterGridManager extends Component {
    @property({type: Node, tooltip: '连线图层'})
    lineLayer: Node = null;
    
    @property({tooltip: '行数'})
    rows: number = 9;
    
    @property({tooltip: '列数'})
    columns: number = 8;
    
    @property({tooltip: '选中字母颜色'})
    selectedColor: Color = new Color(255, 200, 0, 255);
    
    @property({tooltip: '连线颜色'})
    lineColor: Color = new Color(255, 100, 0, 255);
    
    @property({tooltip: '连线宽度'})
    lineWidth: number = 8;

    @property({type: FoundWordHighlighter, tooltip: '已找到单词高亮管理器'})
    foundWordHighlighter: FoundWordHighlighter = null;

    // 存储所有单元格节点的二维数组
    private cellNodes: Node[][] = [];
    // 当前选中的单元格
    private selectedCells: {row: number, col: number}[] = [];
    // 当前拼写的单词
    private currentWord: string = '';
    // 是否正在拖动
    private isDragging: boolean = false;
    // Graphics组件用于绘制连线
    private graphics: Graphics = null;
    // 存储预定义的单词路径信息
    private wordPaths: {word: string, positions: {row: number, col: number}[]}[] = [];
    // 存储所有有效的连接路径（用于快速查找）
    private validPaths: Map<string, {row: number, col: number}[]> = new Map();
    // 优化：按起始位置索引的路径映射，提高查找效率
    private pathsByStartPosition: Map<string, {row: number, col: number}[][]> = new Map();
    // 优化：缓存可能的下一步位置，避免重复计算
    private nextPositionsCache: Map<string, {row: number, col: number}[]> = new Map();
    // 优化：预计算的相邻位置映射
    private adjacentPositions: Map<string, {row: number, col: number}[]> = new Map();

    
    start() {
        console.log("LetterGridManager 开始初始化");

        // 初始化单元格节点数组
        this.initCellNodes();
        // 初始化连线图层
        this.initLineGraphics();

        // 初始化高亮管理器
        this.initFoundWordHighlighter();

        // 设置触摸事件
        this.setupTouchEvents();

        // 监听单词列表准备好的事件
        const scene = director.getScene();
        if (scene) {
            scene.on('words-ready', this.onWordsReady, this);
        }

        // 立即生成预设字母网格，避免闪动
        this.generatePresetGrid();

        console.log('LetterGridManager 初始化完成');
    }

    // 生成预设字母网格，避免界面闪动
    private generatePresetGrid() {
        console.log("生成预设字母网格");

        // 使用预设的单词列表立即生成网格
        const presetWords = ['APPLE', 'BANANA', 'ORANGE', 'GRAPE', 'LEMON', 'CHERRY'];

        // 尝试生成最佳网格配置
        let success = false;
        let attempts = 0;
        const maxAttempts = 5;

        while (!success && attempts < maxAttempts) {
            attempts++;
            console.log(`预设网格生成尝试 ${attempts}/${maxAttempts}`);

            if (this.generateLettersFromWordsSync(presetWords)) {
                success = true;
                console.log("预设网格生成成功");
            }
        }

        if (!success) {
            console.warn("预设网格生成失败，使用随机字母");
            this.generateRandomLetters();
        }
    }

    // 同步版本的字母网格生成，避免异步延迟
    private generateLettersFromWordsSync(words: string[]): boolean {
        console.log("同步生成字母网格，单词:", words);

        if (this.cellNodes.length === 0) {
            console.error("单元格节点未初始化");
            return;
        }

        if (!words || words.length === 0) {
            console.error("单词列表为空");
            return false;
        }

        // 预处理：将所有单词转为大写并按长度排序（从长到短）
        const sortedWords = [...words].map(word => word.toUpperCase())
            .sort((a, b) => b.length - a.length);

        // 初始化空白字母网格
        const grid: string[][] = [];
        for (let row = 0; row < this.rows; row++) {
            const rowLetters: string[] = [];
            for (let col = 0; col < this.columns; col++) {
                rowLetters.push('');
            }
            grid.push(rowLetters);
        }

        // 记录每个单词在网格中的位置
        const wordPositions: {word: string, positions: {row: number, col: number}[]}[] = [];

        // 使用改进的放置算法（同步版本）
        const placedCount = this.improvedPlacement(sortedWords, grid, wordPositions);

        if (placedCount >= Math.max(4, sortedWords.length * 0.8)) {
            console.log(`成功放置 ${placedCount}/${sortedWords.length} 个单词`);

            // 填充剩余的空白单元格
            this.fillEmptyCells(grid);

            // 将生成的字母网格应用到UI
            this.applyGridToUI(grid);

            // 保存单词路径信息并构建有效路径映射
            this.wordPaths = [...wordPositions];
            this.buildValidPathsMap();

            console.log("预设字母网格生成完成");
            return true;
        } else {
            console.warn("预设网格生成失败");
            return false;
        }
    }

    onDestroy() {
        // 移除事件监听
        if (this.node) {
            this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
            this.node.off(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.node.off(Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this.node.off(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
        
        // 移除全局事件监听
        const scene = director.getScene();
        if (scene) {
            scene.off('words-ready', this.onWordsReady, this);
        }
        
        // 移除验证结果监听
        if (this.node) {
            this.node.off('word-validation-result');
        }
    }

    // 处理单词列表准备好的事件
    onWordsReady(words: string[]) {
        console.log('收到单词列表:', words);

        // 检查是否与当前单词列表相同，避免重复生成
        const currentWords = this.wordPaths.map(wp => wp.word);
        const newWords = words ? words.map(w => w.toUpperCase()) : [];

        if (this.arraysEqual(currentWords, newWords)) {
            console.log('单词列表未变化，跳过重新生成');
            return;
        }

        if (words && words.length > 0) {
            console.log('单词列表已变化，重新生成字母网格');
            // 根据单词列表生成字母网格
            this.generateLettersFromWords(words);
        } else {
            // 如果没有收到单词列表，使用随机字母
            console.warn('未收到单词列表，使用随机字母');
            this.generateRandomLetters();
        }
    }

    // 比较两个数组是否相等
    private arraysEqual(arr1: string[], arr2: string[]): boolean {
        if (arr1.length !== arr2.length) return false;

        // 排序后比较，因为顺序可能不同
        const sorted1 = [...arr1].sort();
        const sorted2 = [...arr2].sort();

        for (let i = 0; i < sorted1.length; i++) {
            if (sorted1[i] !== sorted2[i]) return false;
        }

        return true;
    }
    
    // 初始化单元格节点数组
    initCellNodes() {
        console.log("开始初始化单元格节点数组");
        this.cellNodes = [];
        
        if (!this.node) {
            console.error("节点不存在，无法初始化单元格");
            return;
        }
        
        // 遍历所有行
        for (let row = 0; row < this.rows; row++) {
            const rowNodes = [];
            // 获取当前行节点
            const rowNode = this.node.getChildByName(`${row + 1}`);
            
            if (rowNode) {
                console.log(`找到行节点 ${row + 1}`);
                
                // 遍历行中的所有单元格
                for (let col = 0; col < this.columns; col++) {
                    // 尝试查找单元格节点
                    const cellNode = rowNode.getChildByName(`单元格${col + 1}`) || 
                                    rowNode.getChildByName(`单元格 ${col + 1}`) ||
                                    rowNode.getChildByName(`单元格_${col + 1}`) ||
                                    rowNode.getChildByName(`单元格-${col + 1}`) ||
                                    rowNode.getChildByName(`单元格(${col + 1})`) ||
                                    rowNode.getChildByName(`单元格${col + 1}`);
                    
                    if (cellNode) {
                        rowNodes.push(cellNode);
                        console.log(`找到单元格: 行${row + 1}, 列${col + 1}`);
                    } else {
                        console.warn(`未找到单元格: 行${row + 1}, 列${col + 1}`);
                        // 创建一个空节点作为占位符
                        rowNodes.push(null);
                    }
                }
                this.cellNodes.push(rowNodes);
            } else {
                console.warn(`未找到行节点: ${row + 1}`);
            }
        }
        
        console.log(`初始化了 ${this.cellNodes.length} 行单元格`);
        
        // 如果没有找到任何单元格，尝试使用不同的方法
        if (this.cellNodes.length === 0) {
            console.warn("未找到任何单元格，尝试使用不同的方法");
            this.initCellNodesAlternative();
        }
    }
    
    // 替代方法初始化单元格节点数组
    initCellNodesAlternative() {
        console.log("使用替代方法初始化单元格节点数组");
        this.cellNodes = [];
        
        // 遍历所有可能的行节点
        for (let row = 0; row < this.rows; row++) {
            const rowNodes = [];
            
            // 尝试多种可能的行节点命名
            const rowNames = [`${row + 1}`, `Row${row + 1}`, `行${row + 1}`, `行 ${row + 1}`];
            let rowNode = null;
            
            for (const name of rowNames) {
                rowNode = this.node.getChildByName(name);
                if (rowNode) {
                    console.log(`找到行节点 ${name}`);
                    break;
                }
            }
            
            if (rowNode) {
                // 遍历行中的所有单元格
                for (let col = 0; col < this.columns; col++) {
                    // 尝试多种可能的单元格命名
                    const cellNames = [
                        `单元格${col + 1}`, `单元格 ${col + 1}`, `单元格_${col + 1}`,
                        `单元格-${col + 1}`, `单元格(${col + 1})`, `Cell${col + 1}`,
                        `格子${col + 1}`, `格子 ${col + 1}`
                    ];
                    
                    let cellNode = null;
                    for (const name of cellNames) {
                        cellNode = rowNode.getChildByName(name);
                        if (cellNode) {
                            console.log(`找到单元格: ${name}`);
                            break;
                        }
                    }
                    
                    if (cellNode) {
                        rowNodes.push(cellNode);
                    } else {
                        console.error(`未找到单元格: 行${row + 1}, 列${col + 1}`);
                        rowNodes.push(null);
                    }
                }
                this.cellNodes.push(rowNodes);
            }
        }
        
        console.log(`替代方法初始化了 ${this.cellNodes.length} 行单元格`);
    }
    
    // 初始化连线图层
    initLineGraphics() {
        if (!this.lineLayer) {
            console.error('连线图层未设置，请在属性检查器中设置连线图层');
            
            // 尝试查找连线图层
            this.lineLayer = this.node.getChildByName('连线图层');
            if (!this.lineLayer) {
                console.log("尝试创建连线图层");
                this.lineLayer = new Node('连线图层');
                this.lineLayer.parent = this.node;
            }
        }
        
        // 添加Graphics组件用于绘制连线
        this.graphics = this.lineLayer.getComponent(Graphics);
        if (!this.graphics) {
            this.graphics = this.lineLayer.addComponent(Graphics);
        }
        
        // 设置线条样式
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.lineJoin = Graphics.LineJoin.ROUND;
        this.graphics.lineCap = Graphics.LineCap.ROUND;
        this.graphics.strokeColor = this.lineColor;
    }
    
    // 初始化高亮管理器
    initFoundWordHighlighter() {
        if (this.foundWordHighlighter) {
            // 设置字母网格引用
            this.foundWordHighlighter.setCellNodes(this.cellNodes);
            console.log("高亮管理器初始化完成");
        } else {
            console.warn("未设置高亮管理器，请在属性检查器中设置");
        }
    }

    // 设置触摸事件
    setupTouchEvents() {
        if (!this.node) {
            console.error("节点不存在，无法设置触摸事件");
            return;
        }

        // 触摸开始事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        // 触摸移动事件
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        // 触摸结束事件
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        // 触摸取消事件
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }
    
    // 触摸开始处理
    onTouchStart(event: EventTouch) {
        // 重置状态
        this.resetSelection();
        this.isDragging = true;

        // 临时降低已找到单词的高亮，以便用户看清当前选择
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.temporarilyDimHighlights();
        }

        // 获取触摸位置
        const touchPos = event.getUILocation();

        // 检查是否点击了某个单元格
        const cell = this.getCellAtPosition(touchPos);
        if (cell) {
            this.selectCell(cell.row, cell.col);
        }
    }
    
    // 触摸移动处理
    onTouchMove(event: EventTouch) {
        if (!this.isDragging) return;

        // 获取触摸位置
        const touchPos = event.getUILocation();

        // 查找触摸位置下的单元格
        const cellInfo = this.getCellAtPosition(touchPos);

        if (cellInfo) {
            const { row, col } = cellInfo;

            // 检查是否已经选中了这个单元格
            const isAlreadySelected = this.selectedCells.some(cell => cell.row === row && cell.col === col);

            // 如果没有选中过这个单元格，并且可以选择
            if (!isAlreadySelected && this.canSelectCell(row, col)) {
                // 直接选择单元格，canSelectCell方法已经处理了所有连接规则
                this.selectCell(row, col);
            }
        }

        // 更新连线到当前触摸位置
        this.updateLine(touchPos);
    }
    
    // 触摸结束处理
    onTouchEnd(event: EventTouch) {
        if (this.isDragging) {
            // 结束拖动
            this.isDragging = false;
            
            // 验证当前单词
            if (this.currentWord.length > 1) {
                this.validateWord(this.currentWord);
            } else {
                // 如果只选择了一个字母，重置选择
                this.resetSelection();
            }
        }
    }
    
    // 获取指定位置的单元格
    getCellAtPosition(position: Vec2): {row: number, col: number} | null {
        for (let row = 0; row < this.cellNodes.length; row++) {
            for (let col = 0; col < this.cellNodes[row].length; col++) {
                const cellNode = this.cellNodes[row][col];
                
                // 获取单元格在世界坐标系中的边界
                const cellUITransform = cellNode.getComponent(UITransform);
                if (cellUITransform) {
                    // 转换触摸位置到单元格的本地坐标
                    const localPos = cellNode.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(position.x, position.y, 0));
                    
                    // 检查点是否在单元格内
                    const cellSize = cellUITransform.contentSize;
                    const halfWidth = cellSize.width / 2;
                    const halfHeight = cellSize.height / 2;
                    
                    if (localPos.x >= -halfWidth && localPos.x <= halfWidth &&
                        localPos.y >= -halfHeight && localPos.y <= halfHeight) {
                        return { row, col };
                    }
                }
            }
        }
        
        return null;
    }
    
    // 检查是否可以选择指定单元格
    canSelectCell(row: number, col: number): boolean {
        // 检查边界
        if (row < 0 || row >= this.rows || col < 0 || col >= this.columns) {
            return false;
        }

        // 如果没有选中的单元格，可以选择任何单元格
        if (this.selectedCells.length === 0) {
            return true;
        }

        // 检查是否已经选中过这个单元格
        const isAlreadySelected = this.selectedCells.some(cell => cell.row === row && cell.col === col);
        if (isAlreadySelected) {
            return false;
        }

        // 使用预测性路径验证
        if (this.validPaths.size > 0) {
            return this.canSelectCellWithPathPrediction(row, col);
        }

        // 如果没有预定义路径，使用原有的逻辑（向后兼容）
        return this.canSelectCellLegacy(row, col);
    }

    // 使用路径预测的单元格选择验证（性能优化版本）
    private canSelectCellWithPathPrediction(row: number, col: number): boolean {
        // 快速检查：目标位置是否在可能的下一个位置列表中
        const possibleNextPositions = this.getPossibleNextPositions(this.selectedCells);

        // 使用更高效的查找方式
        for (const pos of possibleNextPositions) {
            if (pos.row === row && pos.col === col) {
                return true;
            }
        }

        // 如果不在预测列表中，检查是否是相邻单元格（允许开始新的路径）
        const lastCell = this.selectedCells[this.selectedCells.length - 1];
        const lastPosKey = this.createPositionKey(lastCell);
        const adjacentPositions = this.adjacentPositions.get(lastPosKey) || [];

        // 检查是否是相邻位置
        const isAdjacent = adjacentPositions.some(pos => pos.row === row && pos.col === col);

        if (isAdjacent) {
            // 对于相邻单元格，快速检查新路径是否可能是有效路径的开始
            return this.isValidPathOptimized([...this.selectedCells, { row, col }]);
        }

        return false;
    }

    // 原有的单元格选择逻辑（向后兼容）
    private canSelectCellLegacy(row: number, col: number): boolean {
        // 获取最后选中的单元格
        const lastCell = this.selectedCells[this.selectedCells.length - 1];

        // 计算行和列的差值
        const rowDiff = Math.abs(row - lastCell.row);
        const colDiff = Math.abs(col - lastCell.col);

        // 检查是否是相邻单元格（包括对角线）
        const isAdjacent = (rowDiff <= 1 && colDiff <= 1) && !(rowDiff === 0 && colDiff === 0);

        if (isAdjacent) {
            // 相邻单元格可以直接选择
            return true;
        }

        // 对于非相邻单元格，检查是否在同一直线上
        const isSameRow = row === lastCell.row;
        const isSameCol = col === lastCell.col;
        const isDiagonal = rowDiff === colDiff; // 对角线上行差等于列差

        // 如果不在同一直线上，不能选择
        if (!isSameRow && !isSameCol && !isDiagonal) {
            return false;
        }

        // 对于对角线连接，实施严格限制：只允许连接严格对角线上的相邻单元格
        if (isDiagonal && !isSameRow && !isSameCol) {
            // 对角线连接时，只允许相邻的对角线单元格（rowDiff = 1 且 colDiff = 1）
            if (rowDiff !== 1 || colDiff !== 1) {
                return false;
            }
            return true;
        }

        // 对于同一行或同一列的连接，检查中间是否有连续的已选中单元格
        if (isSameRow || isSameCol) {
            // 计算方向
            const rowDir = row > lastCell.row ? 1 : (row < lastCell.row ? -1 : 0);
            const colDir = col > lastCell.col ? 1 : (col < lastCell.col ? -1 : 0);

            // 检查中间单元格是否都已被选中
            let currentRow = lastCell.row + rowDir;
            let currentCol = lastCell.col + colDir;

            while (currentRow !== row || currentCol !== col) {
                // 检查这个中间单元格是否已经被选中
                const isSelected = this.selectedCells.some(cell =>
                    cell.row === currentRow && cell.col === currentCol);

                if (!isSelected) {
                    // 如果中间有未选中的单元格，不允许跳过选择
                    return false;
                }

                // 移动到下一个单元格
                currentRow += rowDir;
                currentCol += colDir;
            }

            // 所有中间单元格都已选中，可以选择目标单元格
            return true;
        }

        return false;
    }
    
    // 选择单元格
    selectCell(row: number, col: number) {
        // 检查边界
        if (row < 0 || row >= this.rows || col < 0 || col >= this.columns) {
            return;
        }
        
        // 检查是否可以选择
        if (!this.canSelectCell(row, col)) {
            return;
        }
        
        // 添加到选中列表
        this.selectedCells.push({ row, col });
        
        // 获取单元格节点
        const cellNode = this.cellNodes[row][col];
        
        // 高亮显示
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = this.selectedColor;
        }
        
        // 获取字母
        const letterNode = cellNode.getChildByName('字母');
        if (letterNode) {
            const label = letterNode.getComponent(Label);
            if (label) {
                this.currentWord += label.string;
                console.log(`添加字母: ${label.string}, 当前单词: ${this.currentWord}`);
            }
        }
        
        // 更新连线
        this.updateLine();
    }
    
    // 更新连线
    updateLine(currentPos?: Vec2) {
        if (!this.graphics) return;
        
        // 清除之前的线条
        this.graphics.clear();
        
        // 如果没有选中的单元格，不绘制
        if (this.selectedCells.length === 0) return;
        
        // 设置线条样式
        this.graphics.strokeColor = this.lineColor;
        this.graphics.lineWidth = this.lineWidth;
        
        // 获取第一个选中单元格的中心位置
        const firstCell = this.selectedCells[0];
        const firstCellNode = this.cellNodes[firstCell.row][firstCell.col];
        const firstCellWorldPos = firstCellNode.worldPosition;
        const firstCellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(firstCellWorldPos);
        
        // 开始绘制路径
        this.graphics.moveTo(firstCellLocalPos.x, firstCellLocalPos.y);
        
        // 连接所有选中的单元格
        for (let i = 1; i < this.selectedCells.length; i++) {
            const cell = this.selectedCells[i];
            const cellNode = this.cellNodes[cell.row][cell.col];
            const cellWorldPos = cellNode.worldPosition;
            const cellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(cellWorldPos);
            
            this.graphics.lineTo(cellLocalPos.x, cellLocalPos.y);
        }
        
        // 如果有当前触摸位置，连接到当前触摸位置
        if (currentPos && this.isDragging) {
            const lastPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(currentPos.x, currentPos.y, 0));
            this.graphics.lineTo(lastPos.x, lastPos.y);
        }
        
        // 绘制线条
        this.graphics.stroke();
    }
    
    // 验证单词
    validateWord(word: string) {
        console.log(`验证单词: ${word}`);
        
        // 发送事件通知 WordManager
        this.node.emit('word-selected', {
            word: word,
            cells: [...this.selectedCells]
        });
        
        // 监听验证结果
        const onValidationResult = (isValid: boolean) => {
            this.showWordResult(isValid);
            this.node.off('word-validation-result', onValidationResult);
        };
        
        this.node.on('word-validation-result', onValidationResult, this);
    }
    
    // 显示单词验证结果
    showWordResult(isValid: boolean) {
        // 设置连线颜色
        if (this.graphics) {
            this.graphics.strokeColor = isValid ?
                new Color(0, 255, 0, 255) : // 有效单词为绿色
                new Color(255, 0, 0, 255);  // 无效单词为红色
            this.graphics.stroke();
        }

        // 如果单词有效，使用高亮管理器标记为已找到
        if (isValid && this.foundWordHighlighter) {
            const highlightColor = this.foundWordHighlighter.markWordAsFound(this.currentWord, this.selectedCells);
            console.log(`单词 ${this.currentWord} 已标记为高亮，颜色:`, highlightColor);
        }

        // 对选中的单元格应用动画效果
        for (const cell of this.selectedCells) {
            const cellNode = this.cellNodes[cell.row][cell.col];

            // 创建缩放动画
            const scaleUp = tween(cellNode).to(0.1, { scale: new Vec3(1.2, 1.2, 1) });
            const scaleDown = tween(cellNode).to(0.1, { scale: new Vec3(1, 1, 1) });

            // 执行动画序列
            tween(cellNode)
                .sequence(scaleUp, scaleDown)
                .repeat(2)
                .start();
        }

        // 短暂延迟后重置选择状态
        this.scheduleOnce(() => {
            this.resetSelection(isValid);
        }, 1.0);
    }
    
    // 重置选择状态
    resetSelection(wordWasValid: boolean = false) {
        // 恢复所有选中单元格的颜色（但不覆盖已找到单词的高亮）
        for (const cell of this.selectedCells) {
            const cellNode = this.cellNodes[cell.row][cell.col];
            const sprite = cellNode.getComponent(Sprite);

            if (sprite) {
                // 检查这个单元格是否已经被高亮管理器标记
                if (this.foundWordHighlighter && this.foundWordHighlighter.isCellHighlighted(cell.row, cell.col)) {
                    // 如果已经高亮，不要改变颜色，保持高亮状态
                    // 什么都不做，让已找到的单词保持高亮
                } else {
                    // 如果没有高亮，恢复原始颜色
                    sprite.color = new Color(255, 255, 255, 255);
                }
            }
        }

        // 恢复所有已找到单词的高亮（在循环外调用一次）
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.restoreHighlights();
        }

        // 清除连线
        if (this.graphics) {
            this.graphics.clear();
        }

        // 重置状态
        this.selectedCells = [];
        this.currentWord = '';
        this.isDragging = false;

        // 清理动态缓存以释放内存
        this.clearCache();
    }
    
    // 添加一个方法来生成随机字母
    generateRandomLetters() {
        console.log("开始生成随机字母");
        
        if (this.cellNodes.length === 0) {
            console.error("单元格节点未初始化");
            return;
        }
        
        // 常用字母的出现频率（英语）
        const commonLetters = 'EEEEEEEEEEAAAAAAAARRRRRRRIIIIIIIOOOOOOOTTTTTTTNNNNNNNSSSSSSLLLLLCCCCUUUDDDPPPMMHGBFYWKVXZJQ';
        
        for (let row = 0; row < this.cellNodes.length; row++) {
            for (let col = 0; col < this.cellNodes[row].length; col++) {
                const cellNode = this.cellNodes[row][col];
                if (cellNode) {
                    const letterNode = cellNode.getChildByName('字母');
                    if (letterNode) {
                        const label = letterNode.getComponent(Label);
                        if (label) {
                            // 生成随机字母，使用加权随机以获得更自然的分布
                            const randomIndex = Math.floor(Math.random() * commonLetters.length);
                            const randomLetter = commonLetters[randomIndex];
                            label.string = randomLetter;
                        } else {
                            console.error(`单元格 [${row},${col}] 的字母节点没有Label组件`);
                        }
                    } else {
                        console.error(`单元格 [${row},${col}] 没有字母子节点`);
                    }
                }
            }
        }
        
        console.log("随机字母生成完成");

        // 清空路径信息（因为是随机字母，没有预定义路径）
        this.clearAllPathData();
    }
    
    // 添加一个方法来根据单词列表生成字母网格
    generateLettersFromWords(words: string[]) {
        console.log("使用高级算法根据单词列表生成字母网格");

        if (this.cellNodes.length === 0) {
            console.error("单元格节点未初始化");
            return;
        }

        if (!words || words.length === 0) {
            console.error("单词列表为空");
            return;
        }

        // 多次尝试生成，确保成功率
        let attempts = 0;
        const maxAttempts = 10;
        let success = false;

        while (attempts < maxAttempts && !success) {
            attempts++;
            console.log(`第 ${attempts} 次尝试生成字母网格`);

            // 1. 预处理：将所有单词转为大写并按长度排序（从长到短）
            const sortedWords = [...words].map(word => word.toUpperCase())
                .sort((a, b) => b.length - a.length);

            // 2. 初始化空白字母网格
            const grid: string[][] = [];
            for (let row = 0; row < this.rows; row++) {
                const rowLetters: string[] = [];
                for (let col = 0; col < this.columns; col++) {
                    rowLetters.push('');
                }
                grid.push(rowLetters);
            }

            // 3. 记录每个单词在网格中的位置
            const wordPositions: {word: string, positions: {row: number, col: number}[]}[] = [];

            // 4. 使用改进的放置算法
            const placedCount = this.improvedPlacement(sortedWords, grid, wordPositions);

            // 如果成功放置了所有单词，或者放置了大部分单词
            if (placedCount >= sortedWords.length || placedCount >= Math.max(4, sortedWords.length * 0.8)) {
                console.log(`成功放置 ${placedCount}/${sortedWords.length} 个单词`);

                // 5. 填充剩余的空白单元格
                this.fillEmptyCells(grid);

                // 6. 将生成的字母网格应用到UI
                this.applyGridToUI(grid);

                // 7. 保存单词路径信息并构建有效路径映射
                this.wordPaths = [...wordPositions];
                this.buildValidPathsMap();

                // 验证生成的网格是否包含所有单词
                if (this.validateGeneratedGrid(grid, wordPositions)) {
                    console.log("字母网格生成并验证成功!");
                    success = true;
                } else {
                    console.warn("生成的网格验证失败，重新尝试");
                }
            } else {
                console.warn(`只放置了 ${placedCount}/${sortedWords.length} 个单词，重新尝试`);
            }
        }

        if (!success) {
            console.warn("多次尝试后仍无法生成满意的字母网格，使用随机字母");
            this.generateRandomLetters();
            return;
        }

        // 调试：打印路径信息
        this.debugPrintPaths();

        console.log("字母网格生成完成");
    }

    // 公共方法：重新生成字母网格
    public regenerateGrid() {
        console.log("重新生成字母网格");

        // 重置选择状态
        this.resetSelection();

        // 重新获取单词并生成网格
        this.getWordsFromWordManager();
    }

    // 公共方法：获取当前单词列表
    public getCurrentWords(): string[] {
        return this.wordPaths.map(wp => wp.word);
    }

    // 公共方法：检查是否所有单词都已找到
    public areAllWordsFound(): boolean {
        // 这个方法需要与 WordManager 配合实现
        // 暂时返回 false，具体实现需要监听 WordManager 的状态
        return false;
    }

    // 改进的单词放置算法，结合多种策略
    private improvedPlacement(words: string[], grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): number {
        let placedCount = 0;

        // 定义方向（优先使用水平和垂直方向，提高可连接性）
        const primaryDirections = [
            {rowDir: 0, colDir: 1},  // 水平向右
            {rowDir: 1, colDir: 0},  // 垂直向下
        ];

        const secondaryDirections = [
            {rowDir: 1, colDir: 1},  // 对角向右下
            {rowDir: 1, colDir: -1}, // 对角向左下
            {rowDir: 0, colDir: -1}, // 水平向左
            {rowDir: -1, colDir: 0}, // 垂直向上
            {rowDir: -1, colDir: -1},// 对角向左上
            {rowDir: -1, colDir: 1}  // 对角向右上
        ];

        // 第一阶段：使用主要方向放置单词
        for (const word of words) {
            if (this.tryPlaceWordWithDirections(word, grid, wordPositions, primaryDirections)) {
                placedCount++;
                console.log(`成功放置单词: ${word} (主要方向)`);
            }
        }

        // 第二阶段：对未放置的单词使用所有方向
        const allDirections = [...primaryDirections, ...secondaryDirections];
        for (const word of words) {
            // 检查是否已经放置
            if (!wordPositions.some(wp => wp.word === word)) {
                if (this.tryPlaceWordWithDirections(word, grid, wordPositions, allDirections)) {
                    placedCount++;
                    console.log(`成功放置单词: ${word} (所有方向)`);
                }
            }
        }

        // 第三阶段：强制放置剩余单词
        for (const word of words) {
            if (!wordPositions.some(wp => wp.word === word)) {
                if (this.forcePlaceWord(word, grid, wordPositions)) {
                    placedCount++;
                    console.log(`强制放置单词: ${word}`);
                }
            }
        }

        return placedCount;
    }

    // 尝试使用指定方向放置单词
    private tryPlaceWordWithDirections(word: string, grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[], directions: {rowDir: number, colDir: number}[]): boolean {
        // 随机打乱位置顺序，增加多样性
        const positions: {row: number, col: number}[] = [];
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.columns; col++) {
                positions.push({row, col});
            }
        }
        this.shuffleArray(positions);

        // 尝试每个位置
        for (const pos of positions) {
            // 随机打乱方向顺序
            const shuffledDirections = [...directions];
            this.shuffleArray(shuffledDirections);

            for (const direction of shuffledDirections) {
                if (this.canPlaceWord(word, pos.row, pos.col, direction.rowDir, direction.colDir, grid)) {
                    // 放置单词
                    const wordPos: {row: number, col: number}[] = [];
                    for (let i = 0; i < word.length; i++) {
                        const r = pos.row + i * direction.rowDir;
                        const c = pos.col + i * direction.colDir;
                        grid[r][c] = word[i];
                        wordPos.push({row: r, col: c});
                    }

                    wordPositions.push({word, positions: wordPos});
                    return true;
                }
            }
        }

        return false;
    }

    // 强制放置单词（允许覆盖）
    private forcePlaceWord(word: string, grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): boolean {
        // 优先尝试水平放置
        for (let row = 0; row < this.rows; row++) {
            if (this.columns >= word.length) {
                const maxStartCol = this.columns - word.length;
                const startCol = Math.floor(Math.random() * (maxStartCol + 1));

                const positions: {row: number, col: number}[] = [];
                for (let i = 0; i < word.length; i++) {
                    grid[row][startCol + i] = word[i];
                    positions.push({row, col: startCol + i});
                }

                wordPositions.push({word, positions});
                return true;
            }
        }

        // 如果水平放置失败，尝试垂直放置
        for (let col = 0; col < this.columns; col++) {
            if (this.rows >= word.length) {
                const maxStartRow = this.rows - word.length;
                const startRow = Math.floor(Math.random() * (maxStartRow + 1));

                const positions: {row: number, col: number}[] = [];
                for (let i = 0; i < word.length; i++) {
                    grid[startRow + i][col] = word[i];
                    positions.push({row: startRow + i, col});
                }

                wordPositions.push({word, positions});
                return true;
            }
        }

        return false;
    }

    // 验证生成的网格
    private validateGeneratedGrid(grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): boolean {
        // 检查每个单词是否正确放置
        for (const wordPos of wordPositions) {
            const word = wordPos.word;
            const positions = wordPos.positions;

            // 验证位置是否连续且字母正确
            for (let i = 0; i < word.length; i++) {
                const pos = positions[i];
                if (grid[pos.row][pos.col] !== word[i]) {
                    console.error(`单词 ${word} 在位置 [${pos.row},${pos.col}] 的字母不匹配`);
                    return false;
                }
            }

            // 验证位置是否连续
            if (!this.arePositionsContinuous(positions)) {
                console.error(`单词 ${word} 的位置不连续`);
                return false;
            }
        }

        return true;
    }

    // 检查位置是否连续
    private arePositionsContinuous(positions: {row: number, col: number}[]): boolean {
        if (positions.length <= 1) return true;

        // 计算方向向量
        const firstPos = positions[0];
        const secondPos = positions[1];
        const rowDir = secondPos.row - firstPos.row;
        const colDir = secondPos.col - firstPos.col;

        // 验证所有位置都遵循相同的方向
        for (let i = 1; i < positions.length; i++) {
            const expectedRow = firstPos.row + i * rowDir;
            const expectedCol = firstPos.col + i * colDir;

            if (positions[i].row !== expectedRow || positions[i].col !== expectedCol) {
                return false;
            }
        }

        return true;
    }

    // 回溯算法尝试放置所有单词
    private backtrackPlacement(words: string[], grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): boolean {
        // 基本情况：所有单词都已放置
        if (words.length === 0) {
            return true;
        }
        
        // 取出第一个单词
        const word = words[0];
        const remainingWords = words.slice(1);
        
        // 定义所有可能的方向
        const directions = [
            {rowDir: 0, colDir: 1},  // 水平向右
            {rowDir: 1, colDir: 0},  // 垂直向下
            {rowDir: 1, colDir: 1},  // 对角向右下
            {rowDir: 1, colDir: -1}, // 对角向左下
            {rowDir: 0, colDir: -1}, // 水平向左
            {rowDir: -1, colDir: 0}, // 垂直向上
            {rowDir: -1, colDir: -1},// 对角向左上
            {rowDir: -1, colDir: 1}  // 对角向右上
        ];
        
        // 随机打乱方向顺序，增加多样性
        this.shuffleArray(directions);
        
        // 尝试网格中的每个位置
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.columns; col++) {
                // 尝试每个方向
                for (const direction of directions) {
                    if (this.canPlaceWord(word, row, col, direction.rowDir, direction.colDir, grid)) {
                        // 临时放置单词
                        const positions: {row: number, col: number}[] = [];
                        const originalValues: {row: number, col: number, value: string}[] = [];
                        
                        for (let i = 0; i < word.length; i++) {
                            const r = row + i * direction.rowDir;
                            const c = col + i * direction.colDir;
                            // 保存原始值
                            originalValues.push({row: r, col: c, value: grid[r][c]});
                            // 放置新字母
                            grid[r][c] = word[i];
                            positions.push({row: r, col: c});
                        }
                        
                        // 记录单词位置
                        wordPositions.push({word, positions});
                        
                        // 递归尝试放置剩余单词
                        if (this.backtrackPlacement(remainingWords, grid, wordPositions)) {
                            return true; // 成功放置所有单词
                        }
                        
                        // 如果无法放置剩余单词，回溯
                        wordPositions.pop();
                        
                        // 恢复网格原始值
                        for (const item of originalValues) {
                            grid[item.row][item.col] = item.value;
                        }
                    }
                }
            }
        }
        
        // 无法放置当前单词
        return false;
    }

    // 贪心算法放置单词
    private greedyPlacement(words: string[], grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): void {
        // 定义所有可能的方向
        const directions = [
            {rowDir: 0, colDir: 1},  // 水平向右
            {rowDir: 1, colDir: 0},  // 垂直向下
            {rowDir: 1, colDir: 1},  // 对角向右下
            {rowDir: 1, colDir: -1}, // 对角向左下
            {rowDir: 0, colDir: -1}, // 水平向左
            {rowDir: -1, colDir: 0}, // 垂直向上
            {rowDir: -1, colDir: -1},// 对角向左上
            {rowDir: -1, colDir: 1}  // 对角向右上
        ];
        
        // 对每个单词
        for (const word of words) {
            let placed = false;
            
            // 首先尝试与已放置单词共享字母
            if (wordPositions.length > 0) {
                // 计算当前单词中的每个字母
                const letterPositions = new Map<string, {row: number, col: number}[]>();
                
                // 收集已放置单词中的字母位置
                for (const wp of wordPositions) {
                    for (let i = 0; i < wp.word.length; i++) {
                        const letter = wp.word[i];
                        const pos = wp.positions[i];
                        
                        if (!letterPositions.has(letter)) {
                            letterPositions.set(letter, []);
                        }
                        letterPositions.get(letter).push(pos);
                    }
                }
                
                // 尝试与已有字母交叉
                for (let i = 0; i < word.length; i++) {
                    const letter = word[i];
                    
                    if (letterPositions.has(letter)) {
                        for (const pos of letterPositions.get(letter)) {
                            // 尝试每个方向
                            for (const direction of directions) {
                                // 计算起始位置（向后偏移i个位置）
                                const startRow = pos.row - i * direction.rowDir;
                                const startCol = pos.col - i * direction.colDir;
                                
                                if (this.canPlaceWord(word, startRow, startCol, direction.rowDir, direction.colDir, grid)) {
                                    // 放置单词
                                    const positions: {row: number, col: number}[] = [];
                                    for (let j = 0; j < word.length; j++) {
                                        const r = startRow + j * direction.rowDir;
                                        const c = startCol + j * direction.colDir;
                                        grid[r][c] = word[j];
                                        positions.push({row: r, col: c});
                                    }
                                    
                                    wordPositions.push({word, positions});
                                    placed = true;
                                    break;
                                }
                            }
                            
                            if (placed) break;
                        }
                    }
                    
                    if (placed) break;
                }
            }
            
            // 如果无法与已有单词交叉，尝试随机放置
            if (!placed) {
                // 增加尝试次数
                const maxAttempts = 100;
                
                for (let attempt = 0; attempt < maxAttempts && !placed; attempt++) {
                    // 随机选择起始位置
                    const startRow = Math.floor(Math.random() * this.rows);
                    const startCol = Math.floor(Math.random() * this.columns);
                    
                    // 随机选择方向
                    const dirIndex = Math.floor(Math.random() * directions.length);
                    const direction = directions[dirIndex];
                    
                    if (this.canPlaceWord(word, startRow, startCol, direction.rowDir, direction.colDir, grid)) {
                        // 放置单词
                        const positions: {row: number, col: number}[] = [];
                        for (let i = 0; i < word.length; i++) {
                            const r = startRow + i * direction.rowDir;
                            const c = startCol + i * direction.colDir;
                            grid[r][c] = word[i];
                            positions.push({row: r, col: c});
                        }
                        
                        wordPositions.push({word, positions});
                        placed = true;
                    }
                }
            }
            
            // 如果仍然无法放置，使用强制放置
            if (!placed) {
                this.forcePlace(word, grid, wordPositions);
            }
        }
    }

    // 辅助方法：随机打乱数组
    private shuffleArray<T>(array: T[]): void {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
    }

    // 辅助方法：应用网格到UI
    private applyGridToUI(grid: string[][]) {
        for (let row = 0; row < Math.min(grid.length, this.cellNodes.length); row++) {
            for (let col = 0; col < Math.min(grid[row].length, this.cellNodes[row].length); col++) {
                const cellNode = this.cellNodes[row][col];
                if (cellNode) {
                    const letterNode = cellNode.getChildByName('字母');
                    if (letterNode) {
                        const label = letterNode.getComponent(Label);
                        if (label) {
                            label.string = grid[row][col];
                        }
                    }
                }
            }
        }
    }

    // 添加从 WordManager 获取单词的方法
    private getWordsFromWordManager() {
        console.log("尝试从 WordManager 获取单词列表");

        // 查找单词区域节点 - 尝试更多可能的路径
        let wordManagerNode = null;

        // 尝试直接在父节点下查找
        const possibleNames = ['单词管理器', 'WordManager', '单词区域', '上排单词', '单词列表'];
        console.log("在父节点下查找，父节点:", this.node.parent?.name);

        for (const name of possibleNames) {
            const node = this.node.parent?.getChildByName(name);
            if (node) {
                console.log(`找到单词节点: ${name}`);
                wordManagerNode = node;
                break;
            }
        }

        // 如果没找到，尝试在场景中查找
        if (!wordManagerNode) {
            const scene = director.getScene();
            if (scene) {
                console.log("在场景中查找单词管理器节点");
                for (const name of possibleNames) {
                    const node = scene.getChildByName(name);
                    if (node) {
                        console.log(`在场景中找到单词节点: ${name}`);
                        wordManagerNode = node;
                        break;
                    }
                }

                // 如果还是没找到，尝试在 Canvas 下查找
                if (!wordManagerNode) {
                    const canvas = scene.getChildByName('Canvas');
                    if (canvas) {
                        console.log("在 Canvas 下查找单词管理器节点");
                        for (const name of possibleNames) {
                            const node = canvas.getChildByName(name);
                            if (node) {
                                console.log(`在 Canvas 下找到单词节点: ${name}`);
                                wordManagerNode = node;
                                break;
                            }
                        }
                    }
                }
            }
        }

        // 如果找到了节点，尝试获取 WordManager 组件
        if (wordManagerNode) {
            console.log(`找到单词管理器节点: ${wordManagerNode.name}`);

            // 尝试获取 WordManager 组件
            const wordManager = wordManagerNode.getComponent('WordManager');
            if (wordManager) {
                console.log('找到 WordManager 组件');
                console.log('WordManager.words:', wordManager.words);
                console.log('WordManager.usePresetWords:', wordManager.usePresetWords);
                console.log('WordManager.presetWords:', wordManager.presetWords);

                if (wordManager.words && wordManager.words.length > 0) {
                    console.log('从 WordManager 获取单词列表:', wordManager.words);
                    this.generateLettersFromWords(wordManager.words);
                    return;
                } else {
                    console.warn('WordManager 组件存在，但单词列表为空');
                    // 如果使用预设单词但单词列表为空，尝试使用预设单词
                    if (wordManager.usePresetWords && wordManager.presetWords && wordManager.presetWords.length > 0) {
                        console.log('使用预设单词列表:', wordManager.presetWords);
                        this.generateLettersFromWords(wordManager.presetWords);
                        return;
                    }
                }
            } else {
                console.warn(`找到节点 ${wordManagerNode.name}，但没有 WordManager 组件`);
            }
        } else {
            console.warn('未找到任何单词管理器节点');
        }

        // 如果找不到 WordManager 或单词列表为空，使用随机字母
        console.warn('未找到 WordManager 或单词列表为空，使用随机字母');
        this.generateRandomLetters();
    }

    // 添加备用的单词放置方法，用于处理难以放置的单词
    private fallbackPlaceWord(word: string, grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): boolean {
        // 只尝试水平和垂直方向
        const directions = [
            {rowDir: 0, colDir: 1},  // 水平向右
            {rowDir: 1, colDir: 0}   // 垂直向下
        ];
        
        // 系统性地尝试每个位置
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.columns; col++) {
                for (const direction of directions) {
                    if (this.canPlaceWord(word, row, col, direction.rowDir, direction.colDir, grid)) {
                        // 放置单词
                        const positions: {row: number, col: number}[] = [];
                        for (let i = 0; i < word.length; i++) {
                            const r = row + i * direction.rowDir;
                            const c = col + i * direction.colDir;
                            grid[r][c] = word[i];
                            positions.push({row: r, col: c});
                        }
                        
                        // 记录单词位置
                        wordPositions.push({word, positions});
                        return true;
                    }
                }
            }
        }
        
        // 如果还是失败，尝试覆盖一些已有字母
        return this.forcePlace(word, grid, wordPositions);
    }

    // 强制放置单词，允许覆盖一些已有字母
    private forcePlace(word: string, grid: string[][], wordPositions: {word: string, positions: {row: number, col: number}[]}[]): boolean {
        // 尝试水平放置
        for (let row = 0; row < this.rows; row++) {
            if (this.columns >= word.length) {
                const col = Math.floor(Math.random() * (this.columns - word.length + 1));
                const positions: {row: number, col: number}[] = [];
                
                for (let i = 0; i < word.length; i++) {
                    grid[row][col + i] = word[i];
                    positions.push({row, col: col + i});
                }
                
                wordPositions.push({word, positions});
                return true;
            }
        }
        
        return false;
    }

    // 添加必要的方法实现
    // 检查单词是否可以放置在指定位置和方向
    private canPlaceWord(word: string, startRow: number, startCol: number, rowDir: number, colDir: number, grid: string[][]): boolean {
        // 检查单词是否会超出网格边界
        for (let i = 0; i < word.length; i++) {
            const row = startRow + i * rowDir;
            const col = startCol + i * colDir;
            
            // 检查边界
            if (row < 0 || row >= this.rows || col < 0 || col >= this.columns) {
                return false;
            }
            
            // 检查单元格是否已有字母，且与当前单词字母不匹配
            if (grid[row][col] !== '' && grid[row][col] !== word[i]) {
                return false;
            }
        }
        
        return true;
    }

    // 填充空白单元格
    private fillEmptyCells(grid: string[][]) {
        // 常用字母的出现频率（英语）
        const commonLetters = 'EEEEEEEEEEAAAAAAAARRRRRRRIIIIIIIOOOOOOOTTTTTTTNNNNNNNSSSSSSLLLLLCCCCUUUDDDPPPMMHGBFYWKVXZJQ';

        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.columns; col++) {
                if (grid[row][col] === '') {
                    // 生成随机字母，使用加权随机以获得更自然的分布
                    const randomIndex = Math.floor(Math.random() * commonLetters.length);
                    grid[row][col] = commonLetters[randomIndex];
                }
            }
        }
    }

    // 构建有效路径映射，用于快速验证连接路径（性能优化版本）
    private buildValidPathsMap() {
        console.time('构建路径映射');

        // 清空所有缓存
        this.validPaths.clear();
        this.pathsByStartPosition.clear();
        this.nextPositionsCache.clear();
        this.adjacentPositions.clear();

        // 预计算相邻位置映射
        this.precomputeAdjacentPositions();

        let totalPaths = 0;

        for (const wordPath of this.wordPaths) {
            const positions = wordPath.positions;

            // 优化：只为较短的子路径创建映射（长度 <= 6），减少内存使用
            const maxSubPathLength = Math.min(6, positions.length);

            for (let startIdx = 0; startIdx < positions.length; startIdx++) {
                for (let endIdx = startIdx + 1; endIdx <= Math.min(startIdx + maxSubPathLength, positions.length); endIdx++) {
                    const subPath = positions.slice(startIdx, endIdx);
                    const pathKey = this.createPathKeyOptimized(subPath);

                    if (!this.validPaths.has(pathKey)) {
                        this.validPaths.set(pathKey, subPath);
                        totalPaths++;

                        // 按起始位置索引路径
                        const startPosKey = this.createPositionKey(subPath[0]);
                        if (!this.pathsByStartPosition.has(startPosKey)) {
                            this.pathsByStartPosition.set(startPosKey, []);
                        }
                        this.pathsByStartPosition.get(startPosKey).push(subPath);
                    }
                }
            }
        }

        console.timeEnd('构建路径映射');
        console.log(`构建了 ${totalPaths} 个有效路径，${this.pathsByStartPosition.size} 个起始位置`);
    }

    // 预计算相邻位置映射，提高查找效率
    private precomputeAdjacentPositions() {
        for (let row = 0; row < this.rows; row++) {
            for (let col = 0; col < this.columns; col++) {
                const posKey = this.createPositionKey({row, col});
                const adjacent: {row: number, col: number}[] = [];

                // 计算8个方向的相邻位置
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        if (dr === 0 && dc === 0) continue;

                        const newRow = row + dr;
                        const newCol = col + dc;

                        if (newRow >= 0 && newRow < this.rows &&
                            newCol >= 0 && newCol < this.columns) {
                            adjacent.push({row: newRow, col: newCol});
                        }
                    }
                }

                this.adjacentPositions.set(posKey, adjacent);
            }
        }
    }

    // 创建路径的唯一键
    private createPathKey(positions: {row: number, col: number}[]): string {
        return positions.map(pos => `${pos.row},${pos.col}`).join('-');
    }

    // 优化版本：创建路径键，使用更高效的字符串拼接
    private createPathKeyOptimized(positions: {row: number, col: number}[]): string {
        if (positions.length === 0) return '';
        if (positions.length === 1) return `${positions[0].row},${positions[0].col}`;

        // 使用数组join比字符串拼接更高效
        const parts: string[] = new Array(positions.length);
        for (let i = 0; i < positions.length; i++) {
            parts[i] = `${positions[i].row},${positions[i].col}`;
        }
        return parts.join('-');
    }

    // 创建单个位置的键
    private createPositionKey(position: {row: number, col: number}): string {
        return `${position.row},${position.col}`;
    }

    // 验证当前选择的路径是否是有效的预定义路径
    private isValidPath(selectedCells: {row: number, col: number}[]): boolean {
        if (selectedCells.length < 2) {
            return true; // 单个字母总是有效的
        }

        const pathKey = this.createPathKey(selectedCells);
        return this.validPaths.has(pathKey);
    }

    // 优化版本：验证路径是否有效
    private isValidPathOptimized(selectedCells: {row: number, col: number}[]): boolean {
        if (selectedCells.length < 2) {
            return true; // 单个字母总是有效的
        }

        // 使用优化的路径键创建
        const pathKey = this.createPathKeyOptimized(selectedCells);

        // 首先检查完整路径
        if (this.validPaths.has(pathKey)) {
            return true;
        }

        // 如果完整路径不存在，检查是否是某个更长路径的前缀
        const startPosKey = this.createPositionKey(selectedCells[0]);
        const pathsFromStart = this.pathsByStartPosition.get(startPosKey) || [];

        for (const fullPath of pathsFromStart) {
            if (this.isPathPrefix(selectedCells, fullPath)) {
                return true;
            }
        }

        return false;
    }

    // 预测下一个可能的有效位置（性能优化版本）
    private getPossibleNextPositions(currentPath: {row: number, col: number}[]): {row: number, col: number}[] {
        if (currentPath.length === 0) {
            return []; // 没有当前路径，无法预测
        }

        // 使用缓存避免重复计算
        const currentPathKey = this.createPathKeyOptimized(currentPath);
        if (this.nextPositionsCache.has(currentPathKey)) {
            return this.nextPositionsCache.get(currentPathKey);
        }

        const possiblePositions: {row: number, col: number}[] = [];
        const positionSet = new Set<string>(); // 使用Set避免重复

        // 优化：只查找以当前路径起始位置开始的路径
        const startPosKey = this.createPositionKey(currentPath[0]);
        const pathsFromStart = this.pathsByStartPosition.get(startPosKey) || [];

        for (const fullPath of pathsFromStart) {
            // 检查当前路径是否是这个完整路径的前缀
            if (this.isPathPrefix(currentPath, fullPath) && fullPath.length > currentPath.length) {
                const nextPosition = fullPath[currentPath.length];
                const nextPosKey = this.createPositionKey(nextPosition);

                if (!positionSet.has(nextPosKey)) {
                    positionSet.add(nextPosKey);
                    possiblePositions.push(nextPosition);
                }
            }
        }

        // 缓存结果
        this.nextPositionsCache.set(currentPathKey, possiblePositions);

        return possiblePositions;
    }

    // 检查路径A是否是路径B的前缀（优化版本）
    private isPathPrefix(pathA: {row: number, col: number}[], pathB: {row: number, col: number}[]): boolean {
        if (pathA.length > pathB.length) return false;

        for (let i = 0; i < pathA.length; i++) {
            if (pathA[i].row !== pathB[i].row || pathA[i].col !== pathB[i].col) {
                return false;
            }
        }

        return true;
    }

    // 调试方法：打印当前的路径信息
    private debugPrintPaths() {
        console.log("=== 调试：当前路径信息 ===");

        const stats = this.getPerformanceStats();
        console.log(`性能统计:`, stats);

        console.log(`单词路径数量: ${this.wordPaths.length}`);
        console.log(`有效路径数量: ${this.validPaths.size}`);

        for (const wordPath of this.wordPaths) {
            console.log(`单词: ${wordPath.word}`);
            console.log(`路径: ${wordPath.positions.map(p => `(${p.row},${p.col})`).join(' -> ')}`);
        }

        console.log("=== 调试信息结束 ===");
    }

    // 清空所有路径数据和缓存
    private clearAllPathData() {
        this.wordPaths = [];
        this.validPaths.clear();
        this.pathsByStartPosition.clear();
        this.nextPositionsCache.clear();
        this.adjacentPositions.clear();

        console.log("已清空所有路径数据和缓存");
    }

    // 清理缓存（在选择重置时调用）
    private clearCache() {
        this.nextPositionsCache.clear();
        // 保留其他预计算的数据，只清理动态缓存
    }

    // 获取性能统计信息
    private getPerformanceStats(): {
        wordPaths: number,
        validPaths: number,
        pathsByStartPosition: number,
        cacheSize: number,
        adjacentPositions: number
    } {
        return {
            wordPaths: this.wordPaths.length,
            validPaths: this.validPaths.size,
            pathsByStartPosition: this.pathsByStartPosition.size,
            cacheSize: this.nextPositionsCache.size,
            adjacentPositions: this.adjacentPositions.size
        };
    }
}








