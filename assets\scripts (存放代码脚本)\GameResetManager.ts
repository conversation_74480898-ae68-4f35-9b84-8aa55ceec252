import { _decorator, Component, Node } from 'cc';
import { FoundWordHighlighter } from './FoundWordHighlighter';
import { WordManager } from './WordManager';
import { LetterGridManager } from './LetterGridManager';
const { ccclass, property } = _decorator;

/**
 * 游戏重置管理器
 * 专门负责协调各个组件的重置操作，确保游戏状态正确清理
 */
@ccclass('GameResetManager')
export class GameResetManager extends Component {
    @property({type: WordManager, tooltip: '单词管理器'})
    wordManager: WordManager = null;
    
    @property({type: LetterGridManager, tooltip: '字母网格管理器'})
    letterGridManager: LetterGridManager = null;
    
    @property({type: FoundWordHighlighter, tooltip: '已找到单词高亮管理器'})
    foundWordHighlighter: FoundWordHighlighter = null;
    
    @property({tooltip: '是否在重置时显示动画'})
    showResetAnimation: boolean = true;
    
    @property({tooltip: '重置动画持续时间'})
    resetAnimationDuration: number = 0.5;
    
    start() {
        console.log('游戏重置管理器初始化');
        
        // 自动查找组件（如果没有手动设置）
        this.autoFindComponents();
    }
    
    /**
     * 自动查找相关组件
     */
    private autoFindComponents() {
        // 查找 WordManager
        if (!this.wordManager) {
            this.wordManager = this.node.getComponent(WordManager);
            if (!this.wordManager) {
                // 在父节点或子节点中查找
                this.wordManager = this.node.getComponentInParent(WordManager) || 
                                  this.node.getComponentInChildren(WordManager);
            }
        }
        
        // 查找 LetterGridManager
        if (!this.letterGridManager) {
            this.letterGridManager = this.node.getComponent(LetterGridManager);
            if (!this.letterGridManager) {
                this.letterGridManager = this.node.getComponentInParent(LetterGridManager) || 
                                        this.node.getComponentInChildren(LetterGridManager);
            }
        }
        
        // 查找 FoundWordHighlighter
        if (!this.foundWordHighlighter) {
            this.foundWordHighlighter = this.node.getComponent(FoundWordHighlighter);
            if (!this.foundWordHighlighter) {
                this.foundWordHighlighter = this.node.getComponentInParent(FoundWordHighlighter) || 
                                           this.node.getComponentInChildren(FoundWordHighlighter);
            }
        }
        
        console.log('组件查找结果:', {
            wordManager: !!this.wordManager,
            letterGridManager: !!this.letterGridManager,
            foundWordHighlighter: !!this.foundWordHighlighter
        });
    }
    
    /**
     * 完全重置游戏
     */
    public resetGame() {
        console.log('开始重置游戏');
        
        // 1. 重置高亮管理器（最先重置，清除所有视觉效果）
        if (this.foundWordHighlighter) {
            console.log('重置高亮管理器');
            this.foundWordHighlighter.resetAllHighlights();
        } else {
            console.warn('未找到高亮管理器');
        }
        
        // 2. 重置字母网格管理器
        if (this.letterGridManager) {
            console.log('重置字母网格管理器');
            this.letterGridManager.resetSelection();
        } else {
            console.warn('未找到字母网格管理器');
        }
        
        // 3. 重置单词管理器（最后重置，会触发新的单词生成）
        if (this.wordManager) {
            console.log('重置单词管理器');
            this.wordManager.resetGame();
        } else {
            console.warn('未找到单词管理器');
        }
        
        console.log('游戏重置完成');
    }
    
    /**
     * 重新生成游戏（保持当前单词，重新生成网格）
     */
    public regenerateGame() {
        console.log('开始重新生成游戏');
        
        // 1. 重置高亮管理器
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.resetAllHighlights();
        }
        
        // 2. 重新生成字母网格
        if (this.letterGridManager) {
            this.letterGridManager.regenerateGrid();
        }
        
        // 3. 重新生成单词（如果需要）
        if (this.wordManager) {
            this.wordManager.regenerateGame();
        }
        
        console.log('游戏重新生成完成');
    }
    
    /**
     * 软重置（只清除当前选择状态，不重新生成）
     */
    public softReset() {
        console.log('执行软重置');
        
        // 只重置选择状态，保持已找到的单词高亮
        if (this.letterGridManager) {
            this.letterGridManager.resetSelection();
        }
        
        // 恢复高亮显示
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.restoreHighlights();
        }
        
        console.log('软重置完成');
    }
    
    /**
     * 清除所有已找到的单词高亮
     */
    public clearFoundWords() {
        console.log('清除所有已找到的单词高亮');
        
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.resetAllHighlights();
        }
        
        // 通知单词管理器清除已找到状态
        if (this.wordManager) {
            // 这里需要单词管理器提供清除已找到单词的方法
            // this.wordManager.clearFoundWords();
        }
        
        console.log('已找到单词高亮清除完成');
    }
    
    /**
     * 获取游戏状态信息
     */
    public getGameStatus(): {
        foundWordsCount: number,
        totalWords: number,
        foundWords: string[],
        isGameComplete: boolean
    } {
        let foundWordsCount = 0;
        let totalWords = 0;
        let foundWords: string[] = [];
        let isGameComplete = false;
        
        if (this.foundWordHighlighter) {
            foundWordsCount = this.foundWordHighlighter.getFoundWordsCount();
            foundWords = this.foundWordHighlighter.getFoundWords();
        }
        
        if (this.wordManager) {
            // 假设单词管理器有获取总单词数的方法
            // totalWords = this.wordManager.getTotalWordsCount();
            totalWords = 6; // 临时硬编码
        }
        
        isGameComplete = foundWordsCount >= totalWords;
        
        return {
            foundWordsCount,
            totalWords,
            foundWords,
            isGameComplete
        };
    }
    
    /**
     * 检查是否所有单词都已找到
     */
    public isGameComplete(): boolean {
        const status = this.getGameStatus();
        return status.isGameComplete;
    }
    
    /**
     * 获取已找到的单词列表
     */
    public getFoundWords(): string[] {
        if (this.foundWordHighlighter) {
            return this.foundWordHighlighter.getFoundWords();
        }
        return [];
    }
    
    /**
     * 检查特定单词是否已找到
     */
    public isWordFound(word: string): boolean {
        if (this.foundWordHighlighter) {
            return this.foundWordHighlighter.isWordFound(word);
        }
        return false;
    }
    
    /**
     * 手动标记单词为已找到（用于测试或特殊情况）
     */
    public markWordAsFound(word: string, positions: {row: number, col: number}[]) {
        if (this.foundWordHighlighter) {
            this.foundWordHighlighter.markWordAsFound(word, positions);
        }
        
        if (this.wordManager) {
            this.wordManager.markWordAsFound(word, positions);
        }
    }
    
    /**
     * 调试方法：打印当前游戏状态
     */
    public debugPrintStatus() {
        const status = this.getGameStatus();
        console.log('=== 游戏状态 ===');
        console.log(`已找到单词: ${status.foundWordsCount}/${status.totalWords}`);
        console.log('已找到的单词列表:', status.foundWords);
        console.log('游戏是否完成:', status.isGameComplete);
        console.log('===============');
    }
    
    onDestroy() {
        // 清理资源
        console.log('游戏重置管理器销毁');
    }
}
