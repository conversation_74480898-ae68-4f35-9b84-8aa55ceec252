# 🔍 调试高亮问题指南

## 🎯 当前问题

1. **单词显示颜色不生效** - WordManager 中设置了绿色，但单词显示没有变绿
2. **字母高亮消失** - 连接下一个单词时，前一个单词的高亮被清除

## 🛠️ 调试步骤

### 第一步：检查基本设置

1. **运行游戏**
2. **在控制台运行**：
   ```javascript
   // 获取测试脚本
   const testScript = find('Canvas').getComponent('SimpleHighlightTest');
   
   // 打印测试命令
   testScript.printTestCommands();
   ```

### 第二步：测试单词显示颜色

```javascript
// 测试单词显示颜色是否正常
testScript.testWordDisplayColor();
```

这会调用 WordManager 的 `markWordAsFound` 方法，并输出详细的调试信息。

### 第三步：检查 WordManager 状态

```javascript
// 获取 WordManager
const wordManager = find('Canvas').getChildByName('单词管理器').getComponent('WordManager');

// 检查当前设置
console.log('Found Word Color:', wordManager.foundWordColor);
console.log('Word Nodes:', wordManager.wordNodes);
console.log('Words:', wordManager.words);
```

### 第四步：检查字母网格状态

```javascript
// 获取字母网格管理器
const letterGrid = find('Canvas/字母区域').getComponent('LetterGridManager');

// 检查已找到单词的位置
console.log('Found Word Cells:', letterGrid.foundWordCells);
```

### 第五步：手动测试高亮保持

```javascript
// 1. 手动高亮 BANANA
testScript.testBananaHighlight();

// 2. 模拟选择其他单词（这会调用 resetSelection）
letterGrid.resetSelection();

// 3. 检查 BANANA 是否还保持高亮
```

## 🔧 可能的问题和解决方案

### 问题1：单词节点没有正确设置

**检查**：
```javascript
const wordManager = find('Canvas').getChildByName('单词管理器').getComponent('WordManager');
console.log('Word Nodes 数量:', wordManager.wordNodes.length);
wordManager.wordNodes.forEach((node, index) => {
    console.log(`节点 ${index}:`, node ? node.name : 'null');
});
```

**解决**：确保在 WordManager 的属性检查器中正确拖入了所有单词节点。

### 问题2：单词列表不匹配

**检查**：
```javascript
const wordManager = find('Canvas').getChildByName('单词管理器').getComponent('WordManager');
console.log('预设单词:', wordManager.presetWords);
console.log('当前单词:', wordManager.words);
```

**解决**：确保 `words` 数组与 `wordNodes` 数组长度一致。

### 问题3：字母网格节点名称不匹配

**检查**：
```javascript
// 检查字母网格节点名称
const canvas = find('Canvas');
console.log('Canvas 子节点:');
canvas.children.forEach(child => {
    console.log('- ' + child.name);
});
```

**解决**：确保字母网格节点的名称是 `字母区域`、`下排字母` 或其他预期名称。

### 问题4：foundWordCells 没有正确记录

**检查**：
```javascript
// 连接一个单词后检查
const letterGrid = find('Canvas/字母区域').getComponent('LetterGridManager');
console.log('已记录的高亮位置:', Array.from(letterGrid.foundWordCells));
```

## 🎮 完整测试流程

1. **启动游戏**
2. **连接 BANANA 单词**
3. **在控制台运行**：
   ```javascript
   // 检查是否正确记录了高亮位置
   const letterGrid = find('Canvas/字母区域').getComponent('LetterGridManager');
   console.log('BANANA 高亮位置:', Array.from(letterGrid.foundWordCells));
   
   // 检查单词显示颜色
   const wordManager = find('Canvas').getChildByName('单词管理器').getComponent('WordManager');
   console.log('BANANA 是否已找到:', wordManager.foundWords.has('BANANA'));
   ```

4. **尝试连接其他单词**
5. **检查 BANANA 是否保持高亮**

## 📝 预期结果

- **单词显示**：找到的单词应该变成绿色
- **字母高亮**：找到的单词字母应该保持绿色背景
- **高亮保持**：连接其他单词时，已找到单词的高亮不应该消失

如果还有问题，请提供控制台的具体错误信息！
