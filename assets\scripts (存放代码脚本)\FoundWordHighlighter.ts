import { _decorator, Component, Node, Sprite, Color, Label } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 已找到单词高亮管理器
 * 专门负责管理已找到单词在字母网格中的持久高亮显示
 */
@ccclass('FoundWordHighlighter')
export class FoundWordHighlighter extends Component {
    @property({type: [Color], tooltip: '已找到单词的高亮颜色列表'})
    highlightColors: Color[] = [
        new Color(255, 87, 87, 180),   // 红色半透明
        new Color(255, 193, 7, 180),   // 黄色半透明
        new Color(76, 175, 80, 180),   // 绿色半透明
        new Color(33, 150, 243, 180),  // 蓝色半透明
        new Color(156, 39, 176, 180),  // 紫色半透明
        new Color(255, 152, 0, 180),   // 橙色半透明
        new Color(0, 188, 212, 180),   // 青色半透明
        new Color(233, 30, 99, 180),   // 粉色半透明
    ];
    
    @property({type: Color, tooltip: '默认单元格颜色'})
    defaultCellColor: Color = new Color(255, 255, 255, 255);
    
    @property({type: Color, tooltip: '默认字母颜色'})
    defaultLetterColor: Color = new Color(0, 0, 0, 255);
    
    @property({type: Color, tooltip: '高亮字母颜色'})
    highlightLetterColor: Color = new Color(255, 255, 255, 255);
    
    @property({tooltip: '是否显示高亮边框'})
    showHighlightBorder: boolean = true;
    
    @property({tooltip: '高亮透明度'})
    highlightAlpha: number = 180;
    
    // 存储已找到单词的信息
    private foundWords: Map<string, {
        positions: {row: number, col: number}[],
        color: Color,
        wordIndex: number
    }> = new Map();
    
    // 存储单元格的高亮状态
    private cellHighlights: Map<string, {
        color: Color,
        wordCount: number,
        words: string[]
    }> = new Map();
    
    // 颜色索引
    private colorIndex: number = 0;
    
    // 字母网格引用
    private cellNodes: Node[][] = [];
    
    start() {
        console.log('已找到单词高亮管理器初始化');
    }
    
    /**
     * 设置字母网格引用
     */
    public setCellNodes(cellNodes: Node[][]) {
        this.cellNodes = cellNodes;
        console.log(`设置字母网格: ${cellNodes.length}x${cellNodes[0]?.length || 0}`);
    }
    
    /**
     * 标记单词为已找到并高亮显示
     */
    public markWordAsFound(word: string, positions: {row: number, col: number}[]): Color {
        const upperWord = word.toUpperCase();
        
        // 如果已经找到过，返回现有颜色
        if (this.foundWords.has(upperWord)) {
            return this.foundWords.get(upperWord).color;
        }
        
        // 分配新颜色
        const color = this.getNextColor();
        
        // 记录已找到的单词
        this.foundWords.set(upperWord, {
            positions: [...positions],
            color: color,
            wordIndex: this.foundWords.size
        });
        
        // 高亮单词路径
        this.highlightWordPath(positions, color, upperWord);
        
        console.log(`标记单词 ${upperWord} 为已找到，使用颜色:`, color);
        return color;
    }
    
    /**
     * 高亮单词路径
     */
    private highlightWordPath(positions: {row: number, col: number}[], color: Color, word: string) {
        positions.forEach((pos, index) => {
            this.highlightCell(pos.row, pos.col, color, word, index === 0, index === positions.length - 1);
        });
    }
    
    /**
     * 高亮单个单元格
     */
    private highlightCell(row: number, col: number, color: Color, word: string, isStart: boolean = false, isEnd: boolean = false) {
        if (!this.cellNodes || !this.cellNodes[row] || !this.cellNodes[row][col]) {
            console.warn(`单元格 [${row},${col}] 不存在`);
            return;
        }
        
        const cellNode = this.cellNodes[row][col];
        const cellKey = `${row}-${col}`;
        
        // 更新单元格高亮状态
        if (!this.cellHighlights.has(cellKey)) {
            this.cellHighlights.set(cellKey, {
                color: color,
                wordCount: 1,
                words: [word]
            });
        } else {
            const highlight = this.cellHighlights.get(cellKey);
            highlight.wordCount++;
            highlight.words.push(word);
            // 如果多个单词共享同一个单元格，使用混合颜色或最新颜色
            highlight.color = this.blendColors(highlight.color, color);
        }
        
        // 应用视觉效果
        this.applyCellHighlight(cellNode, this.cellHighlights.get(cellKey).color, isStart, isEnd);
    }
    
    /**
     * 应用单元格高亮效果
     */
    private applyCellHighlight(cellNode: Node, color: Color, isStart: boolean, isEnd: boolean) {
        // 高亮背景 - 优先查找 Sprite 组件
        let sprite = cellNode.getComponent(Sprite);
        if (!sprite) {
            // 如果没有 Sprite 组件，尝试添加一个（但要避免与 Label 冲突）
            const label = cellNode.getComponent(Label);
            if (!label) {
                // 只有在没有 Label 组件时才添加 Sprite
                sprite = cellNode.addComponent(Sprite);
            } else {
                // 如果有 Label 组件，创建一个背景子节点
                this.createBackgroundNode(cellNode, color);
                return;
            }
        }

        if (sprite) {
            sprite.color = color;
        }

        // 高亮字母
        const letterNode = cellNode.getChildByName('字母');
        if (letterNode) {
            const label = letterNode.getComponent(Label);
            if (label) {
                label.color = this.highlightLetterColor;

                // 为起始和结束位置添加特殊标记
                if (isStart || isEnd) {
                    label.fontSize = Math.floor(label.fontSize * 1.1);
                }
            }
        } else {
            // 如果字母节点就是当前节点
            const label = cellNode.getComponent(Label);
            if (label) {
                label.color = this.highlightLetterColor;

                if (isStart || isEnd) {
                    label.fontSize = Math.floor(label.fontSize * 1.1);
                }
            }
        }

        // 添加边框效果（如果启用）
        if (this.showHighlightBorder) {
            this.addHighlightBorder(cellNode, color);
        }
    }

    /**
     * 为有 Label 组件的节点创建背景节点
     */
    private createBackgroundNode(cellNode: Node, color: Color) {
        // 检查是否已有背景节点
        let backgroundNode = cellNode.getChildByName('HighlightBackground');
        if (!backgroundNode) {
            backgroundNode = new Node('HighlightBackground');
            backgroundNode.parent = cellNode;

            // 添加 Sprite 组件作为背景
            const backgroundSprite = backgroundNode.addComponent(Sprite);
            // 设置背景颜色
            backgroundSprite.color = color;

            // 设置背景节点的位置和大小
            backgroundNode.setPosition(0, 0, -1); // 放在后面

            // 获取父节点的大小并应用到背景
            const cellTransform = cellNode.getComponent('UITransform');
            if (cellTransform) {
                const backgroundTransform = backgroundNode.addComponent('UITransform');
                backgroundTransform.width = cellTransform.width;
                backgroundTransform.height = cellTransform.height;
            }
        } else {
            // 更新现有背景颜色
            const backgroundSprite = backgroundNode.getComponent(Sprite);
            if (backgroundSprite) {
                backgroundSprite.color = color;
            }
        }
    }

    /**
     * 添加高亮边框
     */
    private addHighlightBorder(cellNode: Node, color: Color) {
        // 检查是否已有边框节点
        let borderNode = cellNode.getChildByName('HighlightBorder');
        if (!borderNode) {
            borderNode = new Node('HighlightBorder');
            borderNode.parent = cellNode;
            
            // 添加Sprite组件作为边框
            const borderSprite = borderNode.addComponent(Sprite);
            // 这里需要设置边框的SpriteFrame，暂时使用颜色
            borderSprite.color = new Color(color.r, color.g, color.b, 255);
        }
        
        // 设置边框位置和大小
        borderNode.setPosition(0, 0, 1); // 稍微提升层级
        const borderSprite = borderNode.getComponent(Sprite);
        if (borderSprite) {
            borderSprite.color = new Color(color.r, color.g, color.b, 255);
        }
    }
    
    /**
     * 混合两种颜色
     */
    private blendColors(color1: Color, color2: Color): Color {
        return new Color(
            Math.floor((color1.r + color2.r) / 2),
            Math.floor((color1.g + color2.g) / 2),
            Math.floor((color1.b + color2.b) / 2),
            this.highlightAlpha
        );
    }
    
    /**
     * 获取下一个颜色
     */
    private getNextColor(): Color {
        const color = this.highlightColors[this.colorIndex % this.highlightColors.length].clone();
        color.a = this.highlightAlpha;
        this.colorIndex++;
        return color;
    }
    
    /**
     * 检查单元格是否被高亮
     */
    public isCellHighlighted(row: number, col: number): boolean {
        const cellKey = `${row}-${col}`;
        return this.cellHighlights.has(cellKey);
    }
    
    /**
     * 获取单元格的高亮信息
     */
    public getCellHighlightInfo(row: number, col: number): {color: Color, words: string[]} | null {
        const cellKey = `${row}-${col}`;
        const highlight = this.cellHighlights.get(cellKey);
        return highlight ? {color: highlight.color, words: [...highlight.words]} : null;
    }
    
    /**
     * 获取已找到的单词数量
     */
    public getFoundWordsCount(): number {
        return this.foundWords.size;
    }
    
    /**
     * 获取所有已找到的单词
     */
    public getFoundWords(): string[] {
        return Array.from(this.foundWords.keys());
    }
    
    /**
     * 检查单词是否已找到
     */
    public isWordFound(word: string): boolean {
        return this.foundWords.has(word.toUpperCase());
    }
    
    /**
     * 获取单词的高亮颜色
     */
    public getWordColor(word: string): Color | null {
        const wordInfo = this.foundWords.get(word.toUpperCase());
        return wordInfo ? wordInfo.color : null;
    }
    
    /**
     * 重置所有高亮
     */
    public resetAllHighlights() {
        console.log('重置所有单词高亮');
        
        // 清除所有单元格的高亮
        this.cellHighlights.forEach((highlight, cellKey) => {
            const [row, col] = cellKey.split('-').map(Number);
            this.clearCellHighlight(row, col);
        });
        
        // 清空数据
        this.foundWords.clear();
        this.cellHighlights.clear();
        this.colorIndex = 0;
    }
    
    /**
     * 清除单个单元格的高亮
     */
    private clearCellHighlight(row: number, col: number) {
        if (!this.cellNodes || !this.cellNodes[row] || !this.cellNodes[row][col]) {
            return;
        }

        const cellNode = this.cellNodes[row][col];

        // 恢复背景颜色
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = this.defaultCellColor;
        }

        // 移除背景节点（如果存在）
        const backgroundNode = cellNode.getChildByName('HighlightBackground');
        if (backgroundNode) {
            backgroundNode.destroy();
        }

        // 恢复字母颜色和大小
        const letterNode = cellNode.getChildByName('字母');
        if (letterNode) {
            const label = letterNode.getComponent(Label);
            if (label) {
                label.color = this.defaultLetterColor;
                // 恢复原始字体大小（这里需要记录原始大小）
            }
        } else {
            // 如果字母节点就是当前节点
            const label = cellNode.getComponent(Label);
            if (label) {
                label.color = this.defaultLetterColor;
            }
        }

        // 移除边框
        const borderNode = cellNode.getChildByName('HighlightBorder');
        if (borderNode) {
            borderNode.destroy();
        }
    }
    
    /**
     * 临时取消高亮（用于选择新单词时）
     */
    public temporarilyDimHighlights() {
        this.cellHighlights.forEach((highlight, cellKey) => {
            const [row, col] = cellKey.split('-').map(Number);
            const cellNode = this.cellNodes[row][col];
            if (cellNode) {
                const sprite = cellNode.getComponent(Sprite);
                if (sprite) {
                    // 降低透明度
                    const dimmedColor = highlight.color.clone();
                    dimmedColor.a = 100;
                    sprite.color = dimmedColor;
                }
            }
        });
    }
    
    /**
     * 恢复高亮（选择完成后）
     */
    public restoreHighlights() {
        this.cellHighlights.forEach((highlight, cellKey) => {
            const [row, col] = cellKey.split('-').map(Number);
            const cellNode = this.cellNodes[row][col];
            if (cellNode) {
                const sprite = cellNode.getComponent(Sprite);
                if (sprite) {
                    sprite.color = highlight.color;
                }
            }
        });
    }
    
    onDestroy() {
        // 清理资源
        this.resetAllHighlights();
    }
}
