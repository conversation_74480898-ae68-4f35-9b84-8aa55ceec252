import { _decorator, Component, Node, Label, Color, director } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 优化的单词管理器
 * 专注于核心功能：单词验证、显示更新
 */
@ccclass('OptimizedWordManager')
export class OptimizedWordManager extends Component {
    @property({type: [Node], tooltip: '单词显示节点列表'})
    wordNodes: Node[] = [];
    
    @property({type: Color, tooltip: '已找到单词的颜色'})
    foundWordColor: Color = new Color(0, 255, 0, 255); // 绿色
    
    @property({type: Color, tooltip: '默认单词颜色'})
    defaultWordColor: Color = new Color(0, 0, 0, 255); // 黑色
    
    @property({tooltip: '是否使用预设单词'})
    usePresetWords: boolean = true;
    
    @property({type: [String], tooltip: '预设单词列表'})
    presetWords: string[] = ['CAT', 'BANANA', 'ELEPHANT', 'DOG', 'STRAWBERRY', 'OWL'];
    
    // 当前使用的单词列表
    private words: string[] = [];
    // 已找到的单词集合
    private foundWords: Set<string> = new Set();

    start() {
        console.log("OptimizedWordManager 启动");
        
        // 初始化单词列表
        if (this.usePresetWords) {
            this.words = [...this.presetWords];
        }
        
        // 显示单词
        this.displayWords();
        
        // 监听单词选择事件
        this.node.on('word-selected', this.onWordSelected, this);
        
        // 通知字母网格准备好
        this.scheduleOnce(() => {
            this.notifyWordsReady();
        }, 0.1);
    }

    // 显示单词
    displayWords() {
        console.log("显示单词列表:", this.words);
        
        for (let i = 0; i < this.wordNodes.length && i < this.words.length; i++) {
            const wordNode = this.wordNodes[i];
            const word = this.words[i];
            
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.string = word;
                    label.color = this.defaultWordColor;
                    console.log(`设置单词节点 ${i}: ${word}`);
                }
            }
        }
    }

    // 处理单词选择事件
    onWordSelected(wordData: {word: string, cells: {row: number, col: number}[]}) {
        const word = wordData.word.toUpperCase();
        console.log(`收到单词选择事件: ${word}`);
        
        const isValid = this.checkWord(word);
        
        if (isValid) {
            console.log(`✅ 找到有效单词: ${word}`);
            this.markWordAsFound(word);
        } else {
            console.log(`❌ 无效单词: ${word}`);
        }
        
        // 发送验证结果
        this.node.emit('word-validation-result', isValid);
    }

    // 检查单词是否有效
    checkWord(word: string): boolean {
        const upperWord = word.toUpperCase();
        return this.words.includes(upperWord) && !this.foundWords.has(upperWord);
    }

    // 标记单词为已找到
    markWordAsFound(word: string) {
        console.log(`标记单词 ${word} 为已找到`);
        
        // 添加到已找到集合
        this.foundWords.add(word);
        
        // 更新单词显示颜色
        this.updateWordDisplay(word);
        
        // 检查是否所有单词都已找到
        this.checkAllWordsFound();
    }

    // 更新单词显示
    updateWordDisplay(word: string) {
        const index = this.words.indexOf(word);
        
        if (index >= 0 && index < this.wordNodes.length) {
            const wordNode = this.wordNodes[index];
            
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                
                if (label) {
                    label.color = this.foundWordColor;
                    console.log(`✅ 单词 ${word} 显示颜色已更新为绿色`);
                } else {
                    console.error(`❌ 单词节点 ${index} 没有 Label 组件`);
                }
            } else {
                console.error(`❌ 单词节点 ${index} 为空`);
            }
        } else {
            console.error(`❌ 单词 ${word} 不在列表中或索引超出范围`);
        }
    }

    // 检查是否所有单词都已找到
    checkAllWordsFound() {
        if (this.foundWords.size === this.words.length) {
            console.log("🎉 所有单词都已找到！");
            this.node.emit('level-completed');
        } else {
            console.log(`进度: ${this.foundWords.size}/${this.words.length} 个单词已找到`);
        }
    }

    // 通知字母网格单词列表准备好
    notifyWordsReady() {
        console.log("通知字母网格单词列表准备好:", this.words);
        
        // 发送全局事件
        const scene = director.getScene();
        if (scene) {
            scene.emit('words-ready', this.words);
        }
        
        // 尝试直接通知字母网格
        const letterGridNode = this.findLetterGridNode();
        if (letterGridNode) {
            const letterGrid = letterGridNode.getComponent('OptimizedLetterGridManager');
            if (letterGrid && letterGrid.onWordsReady) {
                letterGrid.onWordsReady(this.words);
            }
        }
    }

    // 查找字母网格节点
    findLetterGridNode(): Node | null {
        const possibleNames = ['字母区域', '下排字母', 'LetterGrid', '字母网格'];
        
        // 在同级节点中查找
        if (this.node.parent) {
            for (const name of possibleNames) {
                const node = this.node.parent.getChildByName(name);
                if (node) {
                    return node;
                }
            }
        }
        
        // 在 Canvas 下查找
        const scene = director.getScene();
        if (scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                for (const name of possibleNames) {
                    const node = canvas.getChildByName(name);
                    if (node) {
                        return node;
                    }
                }
            }
        }
        
        return null;
    }

    // 重置游戏
    resetGame() {
        console.log("重置游戏");
        
        // 清空已找到的单词
        this.foundWords.clear();
        
        // 重新显示单词
        this.displayWords();
        
        // 通知字母网格重新生成
        this.scheduleOnce(() => {
            this.notifyWordsReady();
        }, 0.1);
    }

    // 获取游戏状态
    getGameStatus() {
        return {
            totalWords: this.words.length,
            foundWords: this.foundWords.size,
            foundWordsList: Array.from(this.foundWords),
            isCompleted: this.foundWords.size === this.words.length
        };
    }

    // 调试：打印当前状态
    debugPrintStatus() {
        console.log('=== 单词管理器状态 ===');
        console.log('单词列表:', this.words);
        console.log('已找到单词:', Array.from(this.foundWords));
        console.log('进度:', `${this.foundWords.size}/${this.words.length}`);
        console.log('单词节点数量:', this.wordNodes.length);
        console.log('===================');
    }

    onDestroy() {
        // 移除事件监听
        if (this.node) {
            this.node.off('word-selected', this.onWordSelected, this);
        }
    }
}
