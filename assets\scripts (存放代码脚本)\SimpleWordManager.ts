import { _decorator, Component, Node, Label, Color } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 简单的单词管理器 - 原始状态
 * 只包含最基本的功能：显示单词列表
 */
@ccclass('SimpleWordManager')
export class SimpleWordManager extends Component {
    @property({type: [Node], tooltip: '单词显示节点列表'})
    wordNodes: Node[] = [];
    
    @property({type: [String], tooltip: '单词列表'})
    words: string[] = ['CAT', 'DOG', 'BIRD', 'FISH', 'LION', 'BEAR'];
    
    @property({type: Color, tooltip: '已找到单词的颜色'})
    foundWordColor: Color = new Color(0, 255, 0, 255); // 绿色

    // 记录已找到的单词
    private foundWords: Set<string> = new Set();

    start() {
        console.log("SimpleWordManager 启动");
        
        // 显示单词
        this.displayWords();
        
        console.log("SimpleWordManager 初始化完成");
    }

    // 显示单词到界面上
    displayWords() {
        console.log("显示单词到界面");
        
        for (let i = 0; i < Math.min(this.words.length, this.wordNodes.length); i++) {
            const wordNode = this.wordNodes[i];
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.string = this.words[i];
                    label.color = new Color(0, 0, 0, 255); // 黑色
                    wordNode.active = true;
                    console.log(`设置单词 ${i}: ${this.words[i]}`);
                }
            }
        }
        
        // 隐藏多余的节点
        for (let i = this.words.length; i < this.wordNodes.length; i++) {
            if (this.wordNodes[i]) {
                this.wordNodes[i].active = false;
            }
        }
    }

    // 检查单词是否有效
    checkWord(word: string): boolean {
        const upperWord = word.toUpperCase();
        return this.words.includes(upperWord) && !this.foundWords.has(upperWord);
    }

    // 标记单词为已找到
    markWordAsFound(word: string) {
        this.foundWords.add(word);
        
        // 更新显示颜色
        const index = this.words.indexOf(word);
        if (index >= 0 && index < this.wordNodes.length) {
            const wordNode = this.wordNodes[index];
            if (wordNode) {
                const label = wordNode.getComponent(Label);
                if (label) {
                    label.color = this.foundWordColor;
                    console.log(`单词 ${word} 已标记为找到`);
                }
            }
        }
        
        // 检查是否全部找到
        if (this.foundWords.size === this.words.length) {
            console.log("🎉 所有单词都已找到！");
        }
    }

    // 重置游戏
    resetGame() {
        this.foundWords.clear();
        this.displayWords();
        console.log("游戏已重置");
    }
}
