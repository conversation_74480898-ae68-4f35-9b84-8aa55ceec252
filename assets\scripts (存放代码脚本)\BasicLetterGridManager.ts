import { _decorator, Component, Node, Label, Sprite, Color, UITransform, Graphics, Vec2, Vec3, EventTouch } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 基础字母网格管理器 - 最初版本
 * 只包含最基本的功能：显示字母、触摸选择、连线、简单验证
 */
@ccclass('BasicLetterGridManager')
export class BasicLetterGridManager extends Component {
    @property({type: Node, tooltip: '连线图层'})
    lineLayer: Node = null;
    
    @property({tooltip: '行数'})
    rows: number = 9;
    
    @property({tooltip: '列数'})
    columns: number = 8;
    
    @property({type: Color, tooltip: '选中颜色'})
    selectedColor: Color = new Color(255, 255, 0, 255); // 黄色
    
    @property({type: Color, tooltip: '连线颜色'})
    lineColor: Color = new Color(255, 100, 0, 255);
    
    @property({tooltip: '连线宽度'})
    lineWidth: number = 8;

    // 存储所有单元格节点的二维数组
    private cellNodes: Node[][] = [];
    // 当前选中的单元格
    private selectedCells: {row: number, col: number}[] = [];
    // 当前拼写的单词
    private currentWord: string = '';
    // 是否正在拖动
    private isDragging: boolean = false;
    // Graphics组件用于绘制连线
    private graphics: Graphics = null;

    start() {
        console.log("BasicLetterGridManager 启动");
        
        // 初始化单元格节点数组
        this.initCellNodes();
        // 初始化连线图层
        this.initLineGraphics();
        // 设置触摸事件
        this.setupTouchEvents();
        // 生成随机字母
        this.generateRandomLetters();
        
        console.log('BasicLetterGridManager 初始化完成');
    }

    // 初始化单元格节点数组
    initCellNodes() {
        console.log("初始化单元格节点数组");
        this.cellNodes = [];
        
        // 遍历所有行
        for (let row = 0; row < this.rows; row++) {
            const rowNodes = [];
            // 获取当前行节点
            const rowNode = this.node.getChildByName(`${row + 1}`);
            
            if (rowNode) {
                // 遍历行中的所有单元格
                for (let col = 0; col < this.columns; col++) {
                    const cellNode = rowNode.getChildByName(`单元格${col + 1}`);
                    
                    if (cellNode) {
                        rowNodes.push(cellNode);
                        console.log(`找到单元格: 行${row + 1}, 列${col + 1}`);
                    } else {
                        console.warn(`未找到单元格: 行${row + 1}, 列${col + 1}`);
                        rowNodes.push(null);
                    }
                }
                this.cellNodes.push(rowNodes);
            } else {
                console.warn(`未找到行节点: ${row + 1}`);
            }
        }
        
        console.log(`初始化了 ${this.cellNodes.length} 行单元格`);
    }

    // 初始化连线图层
    initLineGraphics() {
        if (!this.lineLayer) {
            this.lineLayer = this.node.getChildByName('连线图层');
            if (!this.lineLayer) {
                this.lineLayer = new Node('连线图层');
                this.lineLayer.parent = this.node;
            }
        }
        
        this.graphics = this.lineLayer.getComponent(Graphics);
        if (!this.graphics) {
            this.graphics = this.lineLayer.addComponent(Graphics);
        }
        
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.strokeColor = this.lineColor;
    }

    // 设置触摸事件
    setupTouchEvents() {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    // 生成随机字母
    generateRandomLetters() {
        console.log("生成随机字母");
        
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        
        for (let row = 0; row < this.cellNodes.length; row++) {
            for (let col = 0; col < this.cellNodes[row].length; col++) {
                const cellNode = this.cellNodes[row][col];
                if (cellNode) {
                    const letterNode = cellNode.getChildByName('字母');
                    if (letterNode) {
                        const label = letterNode.getComponent(Label);
                        if (label) {
                            const randomLetter = letters[Math.floor(Math.random() * letters.length)];
                            label.string = randomLetter;
                        }
                    }
                }
            }
        }
    }

    // 触摸开始
    onTouchStart(event: EventTouch) {
        this.resetSelection();
        this.isDragging = true;
        
        const touchPos = event.getUILocation();
        const cell = this.getCellAtPosition(touchPos);
        if (cell) {
            this.selectCell(cell.row, cell.col);
        }
    }

    // 触摸移动
    onTouchMove(event: EventTouch) {
        if (!this.isDragging) return;
        
        const touchPos = event.getUILocation();
        const cell = this.getCellAtPosition(touchPos);
        
        if (cell && this.canSelectCell(cell.row, cell.col)) {
            this.selectCell(cell.row, cell.col);
        }
        
        this.updateLine(touchPos);
    }

    // 触摸结束
    onTouchEnd(event: EventTouch) {
        if (this.isDragging) {
            this.isDragging = false;
            
            if (this.currentWord.length > 1) {
                this.validateWord(this.currentWord);
            } else {
                this.resetSelection();
            }
        }
    }

    // 获取触摸位置的单元格
    getCellAtPosition(position: Vec2): {row: number, col: number} | null {
        for (let row = 0; row < this.cellNodes.length; row++) {
            for (let col = 0; col < this.cellNodes[row].length; col++) {
                const cellNode = this.cellNodes[row][col];
                if (!cellNode) continue;
                
                const cellUITransform = cellNode.getComponent(UITransform);
                if (cellUITransform) {
                    const localPos = cellNode.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(position.x, position.y, 0));
                    const cellSize = cellUITransform.contentSize;
                    const halfWidth = cellSize.width / 2;
                    const halfHeight = cellSize.height / 2;
                    
                    if (localPos.x >= -halfWidth && localPos.x <= halfWidth &&
                        localPos.y >= -halfHeight && localPos.y <= halfHeight) {
                        return { row, col };
                    }
                }
            }
        }
        return null;
    }

    // 检查是否可以选择单元格
    canSelectCell(row: number, col: number): boolean {
        if (row < 0 || row >= this.rows || col < 0 || col >= this.columns) {
            return false;
        }
        
        const isAlreadySelected = this.selectedCells.some(cell => cell.row === row && cell.col === col);
        if (isAlreadySelected) {
            return false;
        }
        
        if (this.selectedCells.length === 0) {
            return true;
        }
        
        // 简单的相邻检查
        const lastCell = this.selectedCells[this.selectedCells.length - 1];
        const rowDiff = Math.abs(row - lastCell.row);
        const colDiff = Math.abs(col - lastCell.col);
        
        return (rowDiff <= 1 && colDiff <= 1) && !(rowDiff === 0 && colDiff === 0);
    }

    // 选择单元格
    selectCell(row: number, col: number) {
        if (!this.canSelectCell(row, col)) return;
        
        this.selectedCells.push({ row, col });
        
        // 高亮显示
        const cellNode = this.cellNodes[row][col];
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = this.selectedColor;
        }
        
        // 获取字母
        const letterNode = cellNode.getChildByName('字母');
        if (letterNode) {
            const label = letterNode.getComponent(Label);
            if (label) {
                this.currentWord += label.string;
                console.log(`当前单词: ${this.currentWord}`);
            }
        }
        
        this.updateLine();
    }

    // 更新连线
    updateLine(currentPos?: Vec2) {
        if (!this.graphics || this.selectedCells.length === 0) return;
        
        this.graphics.clear();
        this.graphics.strokeColor = this.lineColor;
        this.graphics.lineWidth = this.lineWidth;
        
        // 绘制连线
        const firstCell = this.selectedCells[0];
        const firstCellNode = this.cellNodes[firstCell.row][firstCell.col];
        const firstCellWorldPos = firstCellNode.worldPosition;
        const firstCellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(firstCellWorldPos);
        
        this.graphics.moveTo(firstCellLocalPos.x, firstCellLocalPos.y);
        
        for (let i = 1; i < this.selectedCells.length; i++) {
            const cell = this.selectedCells[i];
            const cellNode = this.cellNodes[cell.row][cell.col];
            const cellWorldPos = cellNode.worldPosition;
            const cellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(cellWorldPos);
            this.graphics.lineTo(cellLocalPos.x, cellLocalPos.y);
        }
        
        if (currentPos && this.isDragging) {
            const lastPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(currentPos.x, currentPos.y, 0));
            this.graphics.lineTo(lastPos.x, lastPos.y);
        }
        
        this.graphics.stroke();
    }

    // 验证单词
    validateWord(word: string) {
        console.log(`验证单词: ${word}`);
        
        // 简单验证：长度大于2就算有效
        const isValid = word.length >= 3;
        
        this.showWordResult(isValid);
    }

    // 显示单词结果
    showWordResult(isValid: boolean) {
        console.log(`单词 ${this.currentWord} ${isValid ? '有效' : '无效'}`);
        
        // 设置连线颜色
        if (this.graphics) {
            this.graphics.strokeColor = isValid ? 
                new Color(0, 255, 0, 255) : 
                new Color(255, 0, 0, 255);
            this.graphics.stroke();
        }
        
        // 延迟重置
        this.scheduleOnce(() => {
            this.resetSelection();
        }, 1.0);
    }

    // 重置选择
    resetSelection() {
        // 恢复单元格颜色
        this.selectedCells.forEach(cell => {
            const cellNode = this.cellNodes[cell.row][cell.col];
            const sprite = cellNode.getComponent(Sprite);
            if (sprite) {
                sprite.color = new Color(255, 255, 255, 255); // 白色
            }
        });
        
        // 清除连线
        if (this.graphics) {
            this.graphics.clear();
        }
        
        // 重置状态
        this.selectedCells = [];
        this.currentWord = '';
        this.isDragging = false;
    }
}
