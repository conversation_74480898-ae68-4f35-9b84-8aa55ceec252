import { _decorator, Component, Node, Label, Sprite, Color } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 简单的高亮测试脚本
 * 用于验证基本的高亮功能是否正常工作
 */
@ccclass('SimpleHighlightTest')
export class SimpleHighlightTest extends Component {
    @property({type: Color, tooltip: '高亮颜色'})
    highlightColor: Color = new Color(255, 87, 87, 255); // 红色

    start() {
        console.log('简单高亮测试脚本启动');
    }

    /**
     * 测试高亮指定位置的单元格
     */
    public testHighlightCell(row: number, col: number) {
        console.log(`测试高亮单元格 [${row},${col}]`);
        
        // 获取字母网格管理器
        const letterGridNode = this.getLetterGridNode();
        if (!letterGridNode) {
            console.error('未找到字母网格节点');
            return;
        }

        const letterGridManager = letterGridNode.getComponent('LetterGridManager') as any;
        if (!letterGridManager || !letterGridManager.cellNodes) {
            console.error('未找到字母网格管理器或单元格节点');
            return;
        }

        // 获取指定位置的单元格
        const cellNode = letterGridManager.cellNodes[row]?.[col];
        if (!cellNode) {
            console.error(`单元格 [${row},${col}] 不存在`);
            return;
        }

        // 高亮单元格
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.color = this.highlightColor;
            console.log(`成功高亮单元格 [${row},${col}]`);
        } else {
            console.error(`单元格 [${row},${col}] 没有 Sprite 组件`);
        }
    }

    /**
     * 测试高亮一个单词的所有字母
     */
    public testHighlightWord(positions: {row: number, col: number}[]) {
        console.log('测试高亮单词:', positions);
        
        positions.forEach((pos, index) => {
            this.testHighlightCell(pos.row, pos.col);
        });
    }

    /**
     * 重置所有单元格颜色
     */
    public resetAllCells() {
        console.log('重置所有单元格颜色');
        
        const letterGridNode = this.getLetterGridNode();
        if (!letterGridNode) {
            console.error('未找到字母网格节点');
            return;
        }

        const letterGridManager = letterGridNode.getComponent('LetterGridManager') as any;
        if (!letterGridManager || !letterGridManager.cellNodes) {
            console.error('未找到字母网格管理器或单元格节点');
            return;
        }

        // 重置所有单元格
        letterGridManager.cellNodes.forEach((row: Node[], rowIndex: number) => {
            row.forEach((cellNode: Node, colIndex: number) => {
                if (cellNode) {
                    const sprite = cellNode.getComponent(Sprite);
                    if (sprite) {
                        sprite.color = new Color(255, 255, 255, 255); // 白色
                    }
                }
            });
        });

        console.log('所有单元格颜色已重置');
    }

    /**
     * 获取字母网格节点
     */
    private getLetterGridNode(): Node | null {
        // 尝试多个可能的节点名称
        const possibleNames = ['字母区域', '下排字母', 'LetterGrid', '字母网格', '字母表'];
        
        // 先在同级节点中查找
        if (this.node.parent) {
            for (const name of possibleNames) {
                const node = this.node.parent.getChildByName(name);
                if (node) {
                    return node;
                }
            }
        }

        // 在 Canvas 下查找
        const scene = this.node.scene;
        if (scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                for (const name of possibleNames) {
                    const node = canvas.getChildByName(name);
                    if (node) {
                        return node;
                    }
                }
            }
        }

        return null;
    }

    /**
     * 测试 BANANA 单词高亮（假设 BANANA 在第6行）
     */
    public testBananaHighlight() {
        console.log('测试 BANANA 单词高亮');
        
        // 假设 BANANA 在第6行，从第1列到第6列
        const bananaPositions = [
            {row: 6, col: 1}, // B
            {row: 6, col: 2}, // A
            {row: 6, col: 3}, // N
            {row: 6, col: 4}, // A
            {row: 6, col: 5}, // N
            {row: 6, col: 6}  // A
        ];
        
        this.testHighlightWord(bananaPositions);
    }

    /**
     * 在控制台提供便捷的测试方法
     */
    public printTestCommands() {
        console.log('=== 高亮测试命令 ===');
        console.log('测试单个单元格: testScript.testHighlightCell(row, col)');
        console.log('测试 BANANA 高亮: testScript.testBananaHighlight()');
        console.log('重置所有颜色: testScript.resetAllCells()');
        console.log('==================');
    }
}
