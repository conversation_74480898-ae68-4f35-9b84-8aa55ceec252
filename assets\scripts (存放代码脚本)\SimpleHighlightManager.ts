import { _decorator, Component, Node, Sprite, Color } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 简单高亮管理器
 * 专门处理单词连接后的高亮保持问题
 */
@ccclass('SimpleHighlightManager')
export class SimpleHighlightManager extends Component {
    @property({type: Color, tooltip: '高亮颜色'})
    highlightColor: Color = new Color(0, 255, 0, 255); // 绿色

    @property({type: Color, tooltip: '默认颜色'})
    defaultColor: Color = new Color(255, 255, 255, 255); // 白色

    // 存储已高亮的单元格位置
    private highlightedCells: Set<string> = new Set();

    start() {
        console.log('SimpleHighlightManager 启动');
    }

    /**
     * 高亮指定位置的单元格
     */
    public highlightCells(cells: {row: number, col: number}[], cellNodes: Node[][]) {
        console.log(`高亮 ${cells.length} 个单元格`);
        
        cells.forEach(cell => {
            const cellKey = `${cell.row}-${cell.col}`;
            this.highlightedCells.add(cellKey);
            
            const cellNode = cellNodes[cell.row]?.[cell.col];
            if (cellNode) {
                this.setCellColor(cellNode, this.highlightColor);
                console.log(`✅ 高亮单元格 [${cell.row},${cell.col}]`);
            }
        });
    }

    /**
     * 检查单元格是否已高亮
     */
    public isCellHighlighted(row: number, col: number): boolean {
        const cellKey = `${row}-${col}`;
        return this.highlightedCells.has(cellKey);
    }

    /**
     * 恢复所有已高亮单元格的颜色
     */
    public restoreHighlights(cellNodes: Node[][]) {
        console.log(`恢复 ${this.highlightedCells.size} 个高亮单元格`);
        
        this.highlightedCells.forEach(cellKey => {
            const [row, col] = cellKey.split('-').map(Number);
            const cellNode = cellNodes[row]?.[col];
            
            if (cellNode) {
                this.setCellColor(cellNode, this.highlightColor);
                console.log(`🔄 恢复单元格 [${row},${col}] 高亮`);
            }
        });
    }

    /**
     * 重置指定单元格为默认颜色（如果它们没有被高亮）
     */
    public resetCellsIfNotHighlighted(cells: {row: number, col: number}[], cellNodes: Node[][]) {
        cells.forEach(cell => {
            if (!this.isCellHighlighted(cell.row, cell.col)) {
                const cellNode = cellNodes[cell.row]?.[cell.col];
                if (cellNode) {
                    this.setCellColor(cellNode, this.defaultColor);
                }
            }
        });
    }

    /**
     * 设置单元格颜色（核心方法）
     */
    private setCellColor(cellNode: Node, color: Color) {
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            // 关键：清除自定义材质，确保颜色生效
            sprite.customMaterial = null;
            sprite.color = color.clone();
            
            // 强制刷新渲染
            sprite.markForUpdateRenderData();
        }
    }

    /**
     * 清除所有高亮
     */
    public clearAllHighlights(cellNodes: Node[][]) {
        console.log('清除所有高亮');
        
        this.highlightedCells.forEach(cellKey => {
            const [row, col] = cellKey.split('-').map(Number);
            const cellNode = cellNodes[row]?.[col];
            
            if (cellNode) {
                this.setCellColor(cellNode, this.defaultColor);
            }
        });
        
        this.highlightedCells.clear();
    }

    /**
     * 获取已高亮单元格数量
     */
    public getHighlightedCount(): number {
        return this.highlightedCells.size;
    }

    /**
     * 获取所有已高亮的位置
     */
    public getHighlightedPositions(): {row: number, col: number}[] {
        const positions: {row: number, col: number}[] = [];
        
        this.highlightedCells.forEach(cellKey => {
            const [row, col] = cellKey.split('-').map(Number);
            positions.push({row, col});
        });
        
        return positions;
    }

    /**
     * 调试：打印当前高亮状态
     */
    public debugPrintStatus() {
        console.log('=== 高亮状态 ===');
        console.log(`已高亮单元格数量: ${this.highlightedCells.size}`);
        console.log(`高亮颜色: (${this.highlightColor.r}, ${this.highlightColor.g}, ${this.highlightColor.b}, ${this.highlightColor.a})`);
        
        if (this.highlightedCells.size > 0) {
            console.log('已高亮位置:');
            this.highlightedCells.forEach(cellKey => {
                console.log(`  - ${cellKey}`);
            });
        }
        console.log('================');
    }
}
