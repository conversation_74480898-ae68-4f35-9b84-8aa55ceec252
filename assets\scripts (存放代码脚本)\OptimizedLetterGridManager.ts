import { _decorator, Component, Node, Label, Sprite, Color, UITransform, Graphics, Vec2, Vec3, <PERSON><PERSON><PERSON><PERSON>, tween, director } from 'cc';
import { SimpleHighlightManager } from './SimpleHighlightManager';
const { ccclass, property } = _decorator;

/**
 * 优化的字母网格管理器
 * 专注于核心功能：字母网格、触摸选择、高亮保持
 */
@ccclass('OptimizedLetterGridManager')
export class OptimizedLetterGridManager extends Component {
    @property({type: Node, tooltip: '连线图层'})
    lineLayer: Node = null;
    
    @property({tooltip: '行数'})
    rows: number = 9;
    
    @property({tooltip: '列数'})
    columns: number = 8;
    
    @property({type: Color, tooltip: '选中颜色'})
    selectedColor: Color = new Color(255, 255, 0, 255); // 黄色
    
    @property({type: Color, tooltip: '连线颜色'})
    lineColor: Color = new Color(255, 100, 0, 255);
    
    @property({tooltip: '连线宽度'})
    lineWidth: number = 8;

    @property({type: SimpleHighlightManager, tooltip: '高亮管理器'})
    highlightManager: SimpleHighlightManager = null;

    // 核心数据
    private cellNodes: Node[][] = [];
    private selectedCells: {row: number, col: number}[] = [];
    private currentWord: string = '';
    private isDragging: boolean = false;
    private graphics: Graphics = null;

    start() {
        console.log("OptimizedLetterGridManager 启动");
        
        // 初始化
        this.initCellNodes();
        this.initLineGraphics();
        this.setupTouchEvents();
        
        // 如果没有设置高亮管理器，自动创建一个
        if (!this.highlightManager) {
            this.highlightManager = this.node.addComponent(SimpleHighlightManager);
            console.log("自动创建高亮管理器");
        }
    }

    // 初始化单元格节点
    initCellNodes() {
        console.log("初始化单元格节点");
        
        this.cellNodes = [];
        
        // 遍历所有子节点，按行列组织
        for (let row = 1; row <= this.rows; row++) {
            const rowNodes: Node[] = [];
            
            for (let col = 1; col <= this.columns; col++) {
                // 查找单元格节点
                const cellNode = this.node.getChildByName(`${row}`)?.getChildByName(`单元格${col}`);
                
                if (cellNode) {
                    rowNodes.push(cellNode);
                } else {
                    console.warn(`未找到单元格节点: 行${row} 单元格${col}`);
                    rowNodes.push(null);
                }
            }
            
            this.cellNodes.push(rowNodes);
        }
        
        console.log(`初始化了 ${this.cellNodes.length} 行单元格`);
    }

    // 初始化连线图层
    initLineGraphics() {
        if (!this.lineLayer) {
            this.lineLayer = this.node.getChildByName('连线图层');
            if (!this.lineLayer) {
                this.lineLayer = new Node('连线图层');
                this.lineLayer.parent = this.node;
            }
        }
        
        this.graphics = this.lineLayer.getComponent(Graphics);
        if (!this.graphics) {
            this.graphics = this.lineLayer.addComponent(Graphics);
        }
        
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.strokeColor = this.lineColor;
    }

    // 设置触摸事件
    setupTouchEvents() {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
    }

    // 触摸开始
    onTouchStart(event: EventTouch) {
        this.resetSelection();
        this.isDragging = true;
        
        const touchPos = event.getUILocation();
        const cell = this.getCellAtPosition(touchPos);
        if (cell) {
            this.selectCell(cell.row, cell.col);
        }
    }

    // 触摸移动
    onTouchMove(event: EventTouch) {
        if (!this.isDragging) return;
        
        const touchPos = event.getUILocation();
        const cell = this.getCellAtPosition(touchPos);
        
        if (cell && this.canSelectCell(cell.row, cell.col)) {
            this.selectCell(cell.row, cell.col);
        }
        
        this.updateLine(touchPos);
    }

    // 触摸结束
    onTouchEnd(event: EventTouch) {
        if (this.isDragging) {
            this.isDragging = false;
            
            if (this.currentWord.length > 1) {
                this.validateWord(this.currentWord);
            } else {
                this.resetSelection();
            }
        }
    }

    // 获取触摸位置的单元格
    getCellAtPosition(position: Vec2): {row: number, col: number} | null {
        for (let row = 0; row < this.cellNodes.length; row++) {
            for (let col = 0; col < this.cellNodes[row].length; col++) {
                const cellNode = this.cellNodes[row][col];
                if (!cellNode) continue;
                
                const cellUITransform = cellNode.getComponent(UITransform);
                if (cellUITransform) {
                    const localPos = cellNode.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(position.x, position.y, 0));
                    const cellSize = cellUITransform.contentSize;
                    const halfWidth = cellSize.width / 2;
                    const halfHeight = cellSize.height / 2;
                    
                    if (localPos.x >= -halfWidth && localPos.x <= halfWidth &&
                        localPos.y >= -halfHeight && localPos.y <= halfHeight) {
                        return { row, col };
                    }
                }
            }
        }
        return null;
    }

    // 检查是否可以选择单元格
    canSelectCell(row: number, col: number): boolean {
        if (row < 0 || row >= this.rows || col < 0 || col >= this.columns) {
            return false;
        }
        
        const isAlreadySelected = this.selectedCells.some(cell => cell.row === row && cell.col === col);
        if (isAlreadySelected) {
            return false;
        }
        
        if (this.selectedCells.length === 0) {
            return true;
        }
        
        // 简单的相邻检查
        const lastCell = this.selectedCells[this.selectedCells.length - 1];
        const rowDiff = Math.abs(row - lastCell.row);
        const colDiff = Math.abs(col - lastCell.col);
        
        return (rowDiff <= 1 && colDiff <= 1) && !(rowDiff === 0 && colDiff === 0);
    }

    // 选择单元格
    selectCell(row: number, col: number) {
        if (!this.canSelectCell(row, col)) return;
        
        this.selectedCells.push({ row, col });
        
        const cellNode = this.cellNodes[row][col];
        const sprite = cellNode.getComponent(Sprite);
        if (sprite) {
            sprite.customMaterial = null;
            sprite.color = this.selectedColor;
        }
        
        // 获取字母
        const letterNode = cellNode.getChildByName('字母');
        if (letterNode) {
            const label = letterNode.getComponent(Label);
            if (label) {
                this.currentWord += label.string;
            }
        }
        
        this.updateLine();
    }

    // 更新连线
    updateLine(currentPos?: Vec2) {
        if (!this.graphics || this.selectedCells.length === 0) return;
        
        this.graphics.clear();
        this.graphics.strokeColor = this.lineColor;
        this.graphics.lineWidth = this.lineWidth;
        
        // 绘制连线
        const firstCell = this.selectedCells[0];
        const firstCellNode = this.cellNodes[firstCell.row][firstCell.col];
        const firstCellWorldPos = firstCellNode.worldPosition;
        const firstCellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(firstCellWorldPos);
        
        this.graphics.moveTo(firstCellLocalPos.x, firstCellLocalPos.y);
        
        for (let i = 1; i < this.selectedCells.length; i++) {
            const cell = this.selectedCells[i];
            const cellNode = this.cellNodes[cell.row][cell.col];
            const cellWorldPos = cellNode.worldPosition;
            const cellLocalPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(cellWorldPos);
            this.graphics.lineTo(cellLocalPos.x, cellLocalPos.y);
        }
        
        if (currentPos && this.isDragging) {
            const lastPos = this.lineLayer.getComponent(UITransform).convertToNodeSpaceAR(new Vec3(currentPos.x, currentPos.y, 0));
            this.graphics.lineTo(lastPos.x, lastPos.y);
        }
        
        this.graphics.stroke();
    }

    // 验证单词
    validateWord(word: string) {
        console.log(`验证单词: ${word}`);
        
        this.node.emit('word-selected', {
            word: word,
            cells: [...this.selectedCells]
        });
        
        const onValidationResult = (isValid: boolean) => {
            this.showWordResult(isValid);
            this.node.off('word-validation-result', onValidationResult);
        };
        
        this.node.on('word-validation-result', onValidationResult, this);
    }

    // 显示单词结果
    showWordResult(isValid: boolean) {
        // 设置连线颜色
        if (this.graphics) {
            this.graphics.strokeColor = isValid ? 
                new Color(0, 255, 0, 255) : 
                new Color(255, 0, 0, 255);
            this.graphics.stroke();
        }
        
        // 如果单词有效，使用高亮管理器记录
        if (isValid && this.highlightManager) {
            this.highlightManager.highlightCells(this.selectedCells, this.cellNodes);
        }
        
        // 延迟重置
        this.scheduleOnce(() => {
            this.resetSelection();
        }, 1.0);
    }

    // 重置选择
    resetSelection() {
        // 恢复选中单元格的颜色
        if (this.highlightManager) {
            this.highlightManager.resetCellsIfNotHighlighted(this.selectedCells, this.cellNodes);
            this.highlightManager.restoreHighlights(this.cellNodes);
        }
        
        // 清除连线
        if (this.graphics) {
            this.graphics.clear();
        }
        
        // 重置状态
        this.selectedCells = [];
        this.currentWord = '';
        this.isDragging = false;
    }
}
